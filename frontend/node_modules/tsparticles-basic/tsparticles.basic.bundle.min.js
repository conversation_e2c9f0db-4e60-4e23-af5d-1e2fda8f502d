/*! For license information please see tsparticles.basic.bundle.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var s in i)("object"==typeof exports?exports:t)[s]=i[s]}}(this,(()=>(()=>{"use strict";var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{AnimatableColor:()=>Ue,AnimationOptions:()=>Ge,AnimationValueWithRandom:()=>je,Background:()=>ze,BackgroundMask:()=>Pe,BackgroundMaskCover:()=>Me,Circle:()=>Li,ClickEvent:()=>Oe,Collisions:()=>Je,CollisionsAbsorb:()=>We,CollisionsOverlap:()=>$e,ColorAnimation:()=>Be,DivEvent:()=>ke,Events:()=>Ie,ExternalInteractorBase:()=>Zi,FullScreen:()=>Se,HoverEvent:()=>Te,HslAnimation:()=>He,HslColorManager:()=>ji,Interactivity:()=>De,ManualParticle:()=>Le,Modes:()=>Ee,Move:()=>ci,MoveAngle:()=>Ke,MoveAttract:()=>ti,MoveCenter:()=>ei,MoveGravity:()=>ii,MovePath:()=>si,MoveTrail:()=>ni,Opacity:()=>li,OpacityAnimation:()=>hi,Options:()=>ki,OptionsColor:()=>xe,OutModes:()=>ai,Parallax:()=>Ce,ParticlesBounce:()=>Qe,ParticlesBounceFactor:()=>Ze,ParticlesDensity:()=>di,ParticlesInteractorBase:()=>Qi,ParticlesNumber:()=>ui,ParticlesOptions:()=>Pi,Point:()=>Ii,Range:()=>Ei,RangedAnimationOptions:()=>qe,RangedAnimationValueWithRandom:()=>Ne,Rectangle:()=>Di,ResizeEvent:()=>Re,Responsive:()=>Ae,RgbColorManager:()=>Ni,Shadow:()=>pi,Shape:()=>_i,Size:()=>xi,SizeAnimation:()=>wi,Spin:()=>ri,Stroke:()=>zi,Theme:()=>Ve,ThemeDefault:()=>Fe,ValueWithRandom:()=>Ye,Vector:()=>v,Vector3d:()=>m,ZIndex:()=>Mi,addColorManager:()=>Tt,addEasing:()=>et,alterHsl:()=>ge,areBoundsInside:()=>T,arrayRandomIndex:()=>O,calcExactPositionOrRandomFromSize:()=>wt,calcExactPositionOrRandomFromSizeRanged:()=>xt,calcPositionFromSize:()=>yt,calcPositionOrRandomFromSize:()=>bt,calcPositionOrRandomFromSizeRanged:()=>_t,calculateBounds:()=>R,circleBounce:()=>V,circleBounceDataFromParticle:()=>F,clamp:()=>nt,clear:()=>de,collisionVelocity:()=>gt,colorMix:()=>qt,colorToHsl:()=>Lt,colorToRgb:()=>Dt,deepExtend:()=>I,divMode:()=>A,divModeExecute:()=>D,drawLine:()=>re,drawParticle:()=>ue,drawParticlePlugin:()=>ve,drawPlugin:()=>me,drawShape:()=>pe,drawShapeAfterEffect:()=>fe,drawTriangle:()=>ce,errorPrefix:()=>f,executeOnSingleOrMultiple:()=>H,findItemFromSingleOrMultiple:()=>W,generatedAttribute:()=>i,getDistance:()=>ft,getDistances:()=>pt,getEasing:()=>it,getHslAnimationFromHsl:()=>Nt,getHslFromAnimation:()=>jt,getLinkColor:()=>Xt,getLinkRandomColor:()=>Yt,getLogger:()=>b,getParticleBaseVelocity:()=>vt,getParticleDirectionAngle:()=>mt,getPosition:()=>q,getRandom:()=>ot,getRandomRgbColor:()=>Wt,getRangeMax:()=>lt,getRangeMin:()=>ht,getRangeValue:()=>ct,getSize:()=>X,getStyleFromHsl:()=>Gt,getStyleFromRgb:()=>$t,getValue:()=>ut,hasMatchMedia:()=>x,hslToRgb:()=>Ht,hslaToRgba:()=>Ut,initParticleNumericAnimationValue:()=>$,isArray:()=>J,isBoolean:()=>Y,isDivModeEnabled:()=>E,isFunction:()=>Z,isInArray:()=>P,isNumber:()=>N,isObject:()=>Q,isPointInside:()=>C,isSsr:()=>w,isString:()=>j,itemFromArray:()=>k,itemFromSingleOrMultiple:()=>U,loadBasic:()=>ae,loadFont:()=>S,loadOptions:()=>Si,loadParticlesOptions:()=>Oi,mix:()=>at,mouseDownEvent:()=>s,mouseLeaveEvent:()=>n,mouseMoveEvent:()=>r,mouseOutEvent:()=>a,mouseUpEvent:()=>o,paintBase:()=>he,paintImage:()=>le,parseAlpha:()=>zt,randomInRange:()=>rt,rangeColorToHsl:()=>At,rangeColorToRgb:()=>Et,rectBounce:()=>B,resizeEvent:()=>u,rgbToHsl:()=>Ft,safeMatchMedia:()=>z,safeMutationObserver:()=>M,setLogger:()=>y,setRandom:()=>st,setRangeValue:()=>dt,singleDivModeExecute:()=>L,stringToAlpha:()=>Vt,stringToRgb:()=>Bt,touchCancelEvent:()=>d,touchEndEvent:()=>h,touchMoveEvent:()=>l,touchStartEvent:()=>c,tsParticles:()=>Ji,visibilityChangeEvent:()=>p});const i="generated",s="pointerdown",o="pointerup",n="pointerleave",a="pointerout",r="pointermove",c="touchstart",h="touchend",l="touchmove",d="touchcancel",u="resize",p="visibilitychange",f="tsParticles - Error";class m{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!N(t)&&t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}else{if(void 0===t||void 0===e)throw new Error(`${f} Vector3d not initialized correctly`);this.x=t,this.y=e,this.z=i??0}}static get origin(){return m.create(0,0,0)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return m.create(t.x,t.y,t.z)}static create(t,e,i){return new m(t,e,i)}add(t){return m.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return m.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return m.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**2+this.y**2}mult(t){return m.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){const t=this.length;0!=t&&this.multTo(1/t)}rotate(t){return m.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),0)}setTo(t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}sub(t){return m.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class v extends m{constructor(t,e){super(t,e,0)}static get origin(){return v.create(0,0)}static clone(t){return v.create(t.x,t.y)}static create(t,e){return new v(t,e)}}const g={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function y(t){g.debug=t.debug||g.debug,g.error=t.error||g.error,g.info=t.info||g.info,g.log=t.log||g.log,g.verbose=t.verbose||g.verbose,g.warning=t.warning||g.warning}function b(){return g}function _(t){const e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:o,rectOtherSide:n,velocity:a,factor:r}=t;return s.min<n.min||s.min>n.max||s.max<n.min||s.max>n.max||(i.max>=o.min&&i.max<=(o.max+o.min)/2&&a>0||i.min<=o.max&&i.min>(o.max+o.min)/2&&a<0)&&(e.velocity=a*-r,e.bounced=!0),e}function w(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function x(){return!w()&&"undefined"!=typeof matchMedia}function z(t){if(x())return matchMedia(t)}function M(t){if(!w()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}function P(t,e){return t===e||J(e)&&e.indexOf(t)>-1}async function S(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function O(t){return Math.floor(ot()*t.length)}function k(t,e,i=!0){return t[void 0!==e&&i?e%t.length:O(t)]}function C(t,e,i,s,o){return T(R(t,s??0),e,i,o)}function T(t,e,i,s){let o=!0;return s&&"bottom"!==s||(o=t.top<e.height+i.x),!o||s&&"left"!==s||(o=t.right>i.x),!o||s&&"right"!==s||(o=t.left<e.width+i.y),!o||s&&"top"!==s||(o=t.bottom>i.y),o}function R(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function I(t,...e){for(const i of e){if(null==i)continue;if(!Q(i)){t=i;continue}const e=Array.isArray(i);!e||!Q(t)&&t&&Array.isArray(t)?e||!Q(t)&&t&&!Array.isArray(t)||(t={}):t=[];for(const e in i){if("__proto__"===e)continue;const s=i[e],o=t;o[e]=Q(s)&&Array.isArray(s)?s.map((t=>I(o[e],t))):I(o[e],s)}}return t}function E(t,e){return!!W(e,(e=>e.enable&&P(t,e.mode)))}function D(t,e,i){H(e,(e=>{const s=e.mode;e.enable&&P(t,s)&&L(e,i)}))}function L(t,e){H(t.selectors,(i=>{e(i,t)}))}function A(t,e){if(e&&t)return W(t,(t=>function(t,e){const i=H(e,(e=>t.matches(e)));return J(i)?i.some((t=>t)):i}(e,t.selectors)))}function F(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:v.create(ut(t.options.bounce.horizontal),ut(t.options.bounce.vertical))}}function V(t,e){const{x:i,y:s}=t.velocity.sub(e.velocity),[o,n]=[t.position,e.position],{dx:a,dy:r}=pt(n,o);if(i*a+s*r<0)return;const c=-Math.atan2(r,a),h=t.mass,l=e.mass,d=t.velocity.rotate(c),u=e.velocity.rotate(c),p=gt(d,u,h,l),f=gt(u,d,h,l),m=p.rotate(-c),v=f.rotate(-c);t.velocity.x=m.x*t.factor.x,t.velocity.y=m.y*t.factor.y,e.velocity.x=v.x*e.factor.x,e.velocity.y=v.y*e.factor.y}function B(t,e){const i=R(t.getPosition(),t.getRadius()),s=_({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:ut(t.options.bounce.horizontal)});s.bounced&&(void 0!==s.velocity&&(t.velocity.x=s.velocity),void 0!==s.position&&(t.position.x=s.position));const o=_({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:ut(t.options.bounce.vertical)});o.bounced&&(void 0!==o.velocity&&(t.velocity.y=o.velocity),void 0!==o.position&&(t.position.y=o.position))}function H(t,e){return J(t)?t.map(((t,i)=>e(t,i))):e(t,0)}function U(t,e,i){return J(t)?k(t,e,i):t}function W(t,e){return J(t)?t.find(((t,i)=>e(t,i))):e(t,0)?t:void 0}function $(t,e){const i=t.value,s=t.animation,o={delayTime:1e3*ct(s.delay),enable:s.enable,value:ct(t.value)*e,max:lt(i)*e,min:ht(i)*e,loops:0,maxLoops:ct(s.count),time:0};if(s.enable){switch(o.decay=1-ct(s.decay),s.mode){case"increase":o.status="increasing";break;case"decrease":o.status="decreasing";break;case"random":o.status=ot()>=.5?"increasing":"decreasing"}const t="auto"===s.mode;switch(s.startValue){case"min":o.value=o.min,t&&(o.status="increasing");break;case"max":o.value=o.max,t&&(o.status="decreasing");break;default:o.value=rt(o),t&&(o.status=ot()>=.5?"increasing":"decreasing")}}return o.initialValue=o.value,o}function G(t,e){if(!("percent"===t.mode)){const{mode:e,...i}=t;return i}return"x"in t?{x:t.x/100*e.width,y:t.y/100*e.height}:{width:t.width/100*e.width,height:t.height/100*e.height}}function q(t,e){return G(t,e)}function X(t,e){return G(t,e)}function Y(t){return"boolean"==typeof t}function j(t){return"string"==typeof t}function N(t){return"number"==typeof t}function Z(t){return"function"==typeof t}function Q(t){return"object"==typeof t&&null!==t}function J(t){return Array.isArray(t)}let K=Math.random;const tt=new Map;function et(t,e){tt.get(t)||tt.set(t,e)}function it(t){return tt.get(t)||(t=>t)}function st(t=Math.random){K=t}function ot(){return nt(K(),0,1-1e-16)}function nt(t,e,i){return Math.min(Math.max(t,e),i)}function at(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function rt(t){const e=lt(t);let i=ht(t);return e===i&&(i=0),ot()*(e-i)+i}function ct(t){return N(t)?t:rt(t)}function ht(t){return N(t)?t:t.min}function lt(t){return N(t)?t:t.max}function dt(t,e){if(t===e||void 0===e&&N(t))return t;const i=ht(t),s=lt(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:dt(i,s)}function ut(t){const e=t.random,{enable:i,minimumValue:s}=Y(e)?{enable:e,minimumValue:0}:e;return ct(i?dt(t.value,s):t.value)}function pt(t,e){const i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function ft(t,e){return pt(t,e).distance}function mt(t,e,i){if(N(t))return t*Math.PI/180;switch(t){case"top":return-Math.PI/2;case"top-right":return-Math.PI/4;case"right":return 0;case"bottom-right":return Math.PI/4;case"bottom":return Math.PI/2;case"bottom-left":return 3*Math.PI/4;case"left":return Math.PI;case"top-left":return-3*Math.PI/4;case"inside":return Math.atan2(i.y-e.y,i.x-e.x);case"outside":return Math.atan2(e.y-i.y,e.x-i.x);default:return ot()*Math.PI*2}}function vt(t){const e=v.origin;return e.length=1,e.angle=t,e}function gt(t,e,i,s){return v.create(t.x*(i-s)/(i+s)+2*e.x*s/(i+s),t.y)}function yt(t){return t.position&&void 0!==t.position.x&&void 0!==t.position.y?{x:t.position.x*t.size.width/100,y:t.position.y*t.size.height/100}:void 0}function bt(t){return{x:(t.position?.x??100*ot())*t.size.width/100,y:(t.position?.y??100*ot())*t.size.height/100}}function _t(t){const e={x:void 0!==t.position?.x?ct(t.position.x):void 0,y:void 0!==t.position?.y?ct(t.position.y):void 0};return bt({size:t.size,position:e})}function wt(t){return{x:t.position?.x??ot()*t.size.width,y:t.position?.y??ot()*t.size.height}}function xt(t){const e={x:void 0!==t.position?.x?ct(t.position.x):void 0,y:void 0!==t.position?.y?ct(t.position.y):void 0};return wt({size:t.size,position:e})}function zt(t){return t?t.endsWith("%")?parseFloat(t)/100:parseFloat(t):1}function Mt(t,e,i,s,o,n){!function(t,e){const i=t.options,s=i.move.path;if(!s.enable)return;if(t.lastPathTime<=t.pathDelay)return void(t.lastPathTime+=e.value);const o=t.pathGenerator?.generate(t,e);o&&t.velocity.addTo(o);s.clamp&&(t.velocity.x=nt(t.velocity.x,-1,1),t.velocity.y=nt(t.velocity.y,-1,1));t.lastPathTime-=t.pathDelay}(t,n);const a=t.gravity,r=a?.enable&&a.inverse?-1:1;o&&i&&(t.velocity.x+=o*n.factor/(60*i)),a?.enable&&i&&(t.velocity.y+=r*(a.acceleration*n.factor)/(60*i));const c=t.moveDecay;t.velocity.multTo(c);const h=t.velocity.mult(i);a?.enable&&s>0&&(!a.inverse&&h.y>=0&&h.y>=s||a.inverse&&h.y<=0&&h.y<=-s)&&(h.y=r*s,i&&(t.velocity.y=h.y/i));const l=t.options.zIndex,d=(1-t.zIndexFactor)**l.velocityRate;h.multTo(d);const{position:u}=t;u.addTo(h),e.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}class Pt{constructor(){this._initSpin=t=>{const e=t.container,i=t.options.move.spin;if(!i.enable)return;const s=i.position??{x:50,y:50},o={x:s.x/100*e.canvas.size.width,y:s.y/100*e.canvas.size.height},n=ft(t.getPosition(),o),a=ct(i.acceleration);t.retina.spinAcceleration=a*e.retina.pixelRatio,t.spin={center:o,direction:t.velocity.x>=0?"clockwise":"counter-clockwise",angle:t.velocity.angle,radius:n,acceleration:t.retina.spinAcceleration}}}init(t){const e=t.options.move.gravity;t.gravity={enable:e.enable,acceleration:ct(e.acceleration),inverse:e.inverse},this._initSpin(t)}isEnabled(t){return!t.destroyed&&t.options.move.enable}move(t,e){const i=t.options,s=i.move;if(!s.enable)return;const o=t.container,n=o.retina.pixelRatio,a=function(t){return t.slow.inRange?t.slow.factor:1}(t),r=(t.retina.moveSpeed??=ct(s.speed)*n)*o.retina.reduceFactor,c=t.retina.moveDrift??=ct(t.options.move.drift)*n,h=lt(i.size.value)*n,l=r*(s.size?t.getRadius()/h:1)*a*(e.factor||1)/2,d=t.retina.maxSpeed??o.retina.maxSpeed;s.spin.enable?function(t,e){const i=t.container;if(!t.spin)return;const s={x:"clockwise"===t.spin.direction?Math.cos:Math.sin,y:"clockwise"===t.spin.direction?Math.sin:Math.cos};t.position.x=t.spin.center.x+t.spin.radius*s.x(t.spin.angle),t.position.y=t.spin.center.y+t.spin.radius*s.y(t.spin.angle),t.spin.radius+=t.spin.acceleration;const o=Math.max(i.canvas.size.width,i.canvas.size.height);t.spin.radius>o/2?(t.spin.radius=o/2,t.spin.acceleration*=-1):t.spin.radius<0&&(t.spin.radius=0,t.spin.acceleration*=-1),t.spin.angle+=e/100*(1-t.spin.radius/o)}(t,l):Mt(t,s,l,d,c,e),function(t){const e=t.initialPosition,{dx:i,dy:s}=pt(e,t.position),o=Math.abs(i),n=Math.abs(s),{maxDistance:a}=t.retina,r=a.horizontal,c=a.vertical;if(r||c)if((r&&o>=r||c&&n>=c)&&!t.misplaced)t.misplaced=!!r&&o>r||!!c&&n>c,r&&(t.velocity.x=t.velocity.y/2-t.velocity.x),c&&(t.velocity.y=t.velocity.x/2-t.velocity.y);else if((!r||o<r)&&(!c||n<c)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){const i=t.position,s=t.velocity;r&&(i.x<e.x&&s.x<0||i.x>e.x&&s.x>0)&&(s.x*=-ot()),c&&(i.y<e.y&&s.y<0||i.y>e.y&&s.y>0)&&(s.y*=-ot())}}(t)}}class St{draw(t,e,i){e.circleRange||(e.circleRange={min:0,max:2*Math.PI});const s=e.circleRange;t.arc(0,0,i,s.min,s.max,!1)}getSidesCount(){return 12}particleInit(t,e){const i=e.shapeData,s=i?.angle??{max:360,min:0};e.circleRange=Q(s)?{min:s.min*Math.PI/180,max:s.max*Math.PI/180}:{min:0,max:s*Math.PI/180}}}const Ot="random",kt="mid",Ct=new Map;function Tt(t){Ct.set(t.key,t)}function Rt(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function It(t){for(const[,e]of Ct)if(t.startsWith(e.stringPrefix))return e.parseString(t);const e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,((t,e,i,s,o)=>e+e+i+i+s+s+(void 0!==o?o+o:""))),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(e);return i?{a:void 0!==i[4]?parseInt(i[4],16)/255:1,b:parseInt(i[3],16),g:parseInt(i[2],16),r:parseInt(i[1],16)}:void 0}function Et(t,e,i=!0){if(!t)return;const s=j(t)?{value:t}:t;if(j(s.value))return Dt(s.value,e,i);if(J(s.value))return Et({value:k(s.value,e,i)});for(const[,t]of Ct){const e=t.handleRangeColor(s);if(e)return e}}function Dt(t,e,i=!0){if(!t)return;const s=j(t)?{value:t}:t;if(j(s.value))return s.value===Ot?Wt():Bt(s.value);if(J(s.value))return Dt({value:k(s.value,e,i)});for(const[,t]of Ct){const e=t.handleColor(s);if(e)return e}}function Lt(t,e,i=!0){const s=Dt(t,e,i);return s?Ft(s):void 0}function At(t,e,i=!0){const s=Et(t,e,i);return s?Ft(s):void 0}function Ft(t){const e=t.r/255,i=t.g/255,s=t.b/255,o=Math.max(e,i,s),n=Math.min(e,i,s),a={h:0,l:(o+n)/2,s:0};return o!==n&&(a.s=a.l<.5?(o-n)/(o+n):(o-n)/(2-o-n),a.h=e===o?(i-s)/(o-n):a.h=i===o?2+(s-e)/(o-n):4+(e-i)/(o-n)),a.l*=100,a.s*=100,a.h*=60,a.h<0&&(a.h+=360),a.h>=360&&(a.h-=360),a}function Vt(t){return It(t)?.a}function Bt(t){return It(t)}function Ht(t){const e={b:0,g:0,r:0},i={h:t.h/360,l:t.l/100,s:t.s/100};if(i.s){const t=i.l<.5?i.l*(1+i.s):i.l+i.s-i.l*i.s,s=2*i.l-t;e.r=Rt(s,t,i.h+1/3),e.g=Rt(s,t,i.h),e.b=Rt(s,t,i.h-1/3)}else e.r=e.g=e.b=i.l;return e.r=Math.floor(255*e.r),e.g=Math.floor(255*e.g),e.b=Math.floor(255*e.b),e}function Ut(t){const e=Ht(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function Wt(t){const e=t??0;return{b:Math.floor(rt(dt(e,256))),g:Math.floor(rt(dt(e,256))),r:Math.floor(rt(dt(e,256)))}}function $t(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??1})`}function Gt(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??1})`}function qt(t,e,i,s){let o=t,n=e;return void 0===o.r&&(o=Ht(t)),void 0===n.r&&(n=Ht(e)),{b:at(o.b,n.b,i,s),g:at(o.g,n.g,i,s),r:at(o.r,n.r,i,s)}}function Xt(t,e,i){if(i===Ot)return Wt();if(i!==kt)return i;{const i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return qt(i,s,t.getRadius(),e.getRadius());{const t=i??s;if(t)return Ht(t)}}}function Yt(t,e,i){const s=j(t)?t:t.value;return s===Ot?i?Et({value:s}):e?Ot:kt:s===kt?kt:Et({value:s})}function jt(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function Nt(t,e,i){const s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(Zt(s.h,e.h,i),Zt(s.s,e.s,i),Zt(s.l,e.l,i)),s}function Zt(t,e,i){t.enable=e.enable,t.enable?(t.velocity=ct(e.speed)/100*i,t.decay=1-ct(e.decay),t.status="increasing",t.loops=0,t.maxLoops=ct(e.count),t.time=0,t.delayTime=1e3*ct(e.delay),e.sync||(t.velocity*=ot(),t.value*=ot()),t.initialValue=t.value):t.velocity=0}function Qt(t,e,i,s,o){if(!e||!i.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=t.value),(e.delayTime??0)>0&&e.time<(e.delayTime??0))return;const n=rt(i.offset),a=(e.velocity??0)*t.factor+3.6*n,r=e.decay??1;o&&"increasing"!==e.status?(e.value-=a,e.value<0&&(e.loops||(e.loops=0),e.loops++,e.status="increasing",e.value+=e.value)):(e.value+=a,e.value>s&&(e.loops||(e.loops=0),e.loops++,o&&(e.status="decreasing",e.value-=e.value%s))),e.velocity&&1!==r&&(e.velocity*=r),e.value>s&&(e.value%=s)}class Jt{constructor(t){this.container=t}init(t){const e=At(t.options.color,t.id,t.options.reduceDuplicates);e&&(t.color=Nt(e,t.options.color.animation,this.container.retina.reduceFactor))}isEnabled(t){const{h:e,s:i,l:s}=t.options.color.animation,{color:o}=t;return!t.destroyed&&!t.spawning&&(void 0!==o?.h.value&&e.enable||void 0!==o?.s.value&&i.enable||void 0!==o?.l.value&&s.enable)}update(t,e){!function(t,e){const{h:i,s,l:o}=t.options.color.animation,{color:n}=t;if(!n)return;const{h:a,s:r,l:c}=n;a&&Qt(e,a,i,360,!1),r&&Qt(e,r,s,100,!0),c&&Qt(e,c,o,100,!0)}(t,e)}}class Kt{constructor(t){this.container=t}init(t){const e=t.options.opacity;t.opacity=$(e,1);const i=e.animation;i.enable&&(t.opacity.velocity=ct(i.speed)/100*this.container.retina.reduceFactor,i.sync||(t.opacity.velocity*=ot()))}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.opacity&&t.opacity.enable&&((t.opacity.maxLoops??0)<=0||(t.opacity.maxLoops??0)>0&&(t.opacity.loops??0)<(t.opacity.maxLoops??0))}reset(t){t.opacity&&(t.opacity.time=0,t.opacity.loops=0)}update(t,e){this.isEnabled(t)&&function(t,e){const i=t.opacity;if(t.destroyed||!i?.enable||(i.maxLoops??0)>0&&(i.loops??0)>(i.maxLoops??0))return;const s=i.min,o=i.max,n=i.decay??1;if(i.time||(i.time=0),(i.delayTime??0)>0&&i.time<(i.delayTime??0)&&(i.time+=e.value),!((i.delayTime??0)>0&&i.time<(i.delayTime??0))){switch(i.status){case"increasing":i.value>=o?(i.status="decreasing",i.loops||(i.loops=0),i.loops++):i.value+=(i.velocity??0)*e.factor;break;case"decreasing":i.value<=s?(i.status="increasing",i.loops||(i.loops=0),i.loops++):i.value-=(i.velocity??0)*e.factor}i.velocity&&1!==i.decay&&(i.velocity*=n),function(t,e,i,s){switch(t.options.opacity.animation.destroy){case"max":e>=s&&t.destroy();break;case"min":e<=i&&t.destroy()}}(t,i.value,s,o),t.destroyed||(i.value=nt(i.value,s,o))}}(t,e)}}class te{constructor(t){this.container=t,this.modes=["bounce","bounce-vertical","bounce-horizontal","bounceVertical","bounceHorizontal","split"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;let n=!1;for(const[,s]of o.plugins)if(void 0!==s.particleBounce&&(n=s.particleBounce(t,i,e)),n)break;if(n)return;const a=t.getPosition(),r=t.offset,c=t.getRadius(),h=R(a,c),l=o.canvas.size;!function(t){if("bounce"!==t.outMode&&"bounce-horizontal"!==t.outMode&&"bounceHorizontal"!==t.outMode&&"split"!==t.outMode||"left"!==t.direction&&"right"!==t.direction)return;t.bounds.right<0&&"left"===t.direction?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&"right"===t.direction&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);const e=t.particle.velocity.x;let i=!1;if("right"===t.direction&&t.bounds.right>=t.canvasSize.width&&e>0||"left"===t.direction&&t.bounds.left<=0&&e<0){const e=ut(t.particle.options.bounce.horizontal);t.particle.velocity.x*=-e,i=!0}if(!i)return;const s=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&"right"===t.direction?t.particle.position.x=t.canvasSize.width-s:t.bounds.left<=0&&"left"===t.direction&&(t.particle.position.x=s),"split"===t.outMode&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:h,canvasSize:l,offset:r,size:c}),function(t){if("bounce"!==t.outMode&&"bounce-vertical"!==t.outMode&&"bounceVertical"!==t.outMode&&"split"!==t.outMode||"bottom"!==t.direction&&"top"!==t.direction)return;t.bounds.bottom<0&&"top"===t.direction?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&"bottom"===t.direction&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);const e=t.particle.velocity.y;let i=!1;if("bottom"===t.direction&&t.bounds.bottom>=t.canvasSize.height&&e>0||"top"===t.direction&&t.bounds.top<=0&&e<0){const e=ut(t.particle.options.bounce.vertical);t.particle.velocity.y*=-e,i=!0}if(!i)return;const s=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&"bottom"===t.direction?t.particle.position.y=t.canvasSize.height-s:t.bounds.top<=0&&"top"===t.direction&&(t.particle.position.y=s),"split"===t.outMode&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:h,canvasSize:l,offset:r,size:c})}}class ee{constructor(t){this.container=t,this.modes=["destroy"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case"normal":case"outside":if(C(t.position,o.canvas.size,v.origin,t.getRadius(),e))return;break;case"inside":{const{dx:e,dy:i}=pt(t.position,t.moveCenter),{x:s,y:o}=t.velocity;if(s<0&&e>t.moveCenter.radius||o<0&&i>t.moveCenter.radius||s>=0&&e<-t.moveCenter.radius||o>=0&&i<-t.moveCenter.radius)return;break}}o.particles.remove(t,void 0,!0)}}class ie{constructor(t){this.container=t,this.modes=["none"]}update(t,e,i,s){if(!this.modes.includes(s))return;if(t.options.move.distance.horizontal&&("left"===e||"right"===e)||t.options.move.distance.vertical&&("top"===e||"bottom"===e))return;const o=t.options.move.gravity,n=this.container,a=n.canvas.size,r=t.getRadius();if(o.enable){const i=t.position;(!o.inverse&&i.y>a.height+r&&"bottom"===e||o.inverse&&i.y<-r&&"top"===e)&&n.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=a.height+r||t.velocity.y<0&&t.position.y>=-r||t.velocity.x>0&&t.position.x<=a.width+r||t.velocity.x<0&&t.position.x>=-r)return;C(t.position,n.canvas.size,v.origin,r,e)||n.particles.remove(t)}}}class se{constructor(t){this.container=t,this.modes=["out"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case"inside":{const{x:e,y:i}=t.velocity,s=v.origin;s.length=t.moveCenter.radius,s.angle=t.velocity.angle+Math.PI,s.addTo(v.create(t.moveCenter));const{dx:n,dy:a}=pt(t.position,s);if(e<=0&&n>=0||i<=0&&a>=0||e>=0&&n<=0||i>=0&&a<=0)return;t.position.x=Math.floor(rt({min:0,max:o.canvas.size.width})),t.position.y=Math.floor(rt({min:0,max:o.canvas.size.height}));const{dx:r,dy:c}=pt(t.position,t.moveCenter);t.direction=Math.atan2(-c,-r),t.velocity.angle=t.direction;break}default:if(C(t.position,o.canvas.size,v.origin,t.getRadius(),e))return;switch(t.outType){case"outside":{t.position.x=Math.floor(rt({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor(rt({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;const{dx:e,dy:i}=pt(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case"normal":{const i=t.options.move.warp,s=o.canvas.size,n={bottom:s.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:s.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},a=t.getRadius(),r=R(t.position,a);"right"===e&&r.left>s.width+t.offset.x?(t.position.x=n.left,t.initialPosition.x=t.position.x,i||(t.position.y=ot()*s.height,t.initialPosition.y=t.position.y)):"left"===e&&r.right<-t.offset.x&&(t.position.x=n.right,t.initialPosition.x=t.position.x,i||(t.position.y=ot()*s.height,t.initialPosition.y=t.position.y)),"bottom"===e&&r.top>s.height+t.offset.y?(i||(t.position.x=ot()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.top,t.initialPosition.y=t.position.y):"top"===e&&r.bottom<-t.offset.y&&(i||(t.position.x=ot()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.bottom,t.initialPosition.y=t.position.y);break}}}}}class oe{constructor(t){this.container=t,this._updateOutMode=(t,e,i,s)=>{for(const o of this.updaters)o.update(t,s,e,i)},this.updaters=[new te(t),new ee(t),new se(t),new ie(t)]}init(){}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){const i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,"bottom"),this._updateOutMode(t,e,i.left??i.default,"left"),this._updateOutMode(t,e,i.right??i.default,"right"),this._updateOutMode(t,e,i.top??i.default,"top")}}class ne{init(t){const e=t.container,i=t.options.size.animation;i.enable&&(t.size.velocity=(t.retina.sizeAnimationSpeed??e.retina.sizeAnimationSpeed)/100*e.retina.reduceFactor,i.sync||(t.size.velocity*=ot()))}isEnabled(t){return!t.destroyed&&!t.spawning&&t.size.enable&&((t.size.maxLoops??0)<=0||(t.size.maxLoops??0)>0&&(t.size.loops??0)<(t.size.maxLoops??0))}reset(t){t.size.loops=0}update(t,e){this.isEnabled(t)&&function(t,e){const i=t.size;if(t.destroyed||!i||!i.enable||(i.maxLoops??0)>0&&(i.loops??0)>(i.maxLoops??0))return;const s=(i.velocity??0)*e.factor,o=i.min,n=i.max,a=i.decay??1;if(i.time||(i.time=0),(i.delayTime??0)>0&&i.time<(i.delayTime??0)&&(i.time+=e.value),!((i.delayTime??0)>0&&i.time<(i.delayTime??0))){switch(i.status){case"increasing":i.value>=n?(i.status="decreasing",i.loops||(i.loops=0),i.loops++):i.value+=s;break;case"decreasing":i.value<=o?(i.status="increasing",i.loops||(i.loops=0),i.loops++):i.value-=s}i.velocity&&1!==a&&(i.velocity*=a),function(t,e,i,s){switch(t.options.size.animation.destroy){case"max":e>=s&&t.destroy();break;case"min":e<=i&&t.destroy()}}(t,i.value,o,n),t.destroyed||(i.value=nt(i.value,o,n))}}(t,e)}}async function ae(t,e=!0){await async function(t,e=!0){await t.addMover("base",(()=>new Pt),e)}(t,!1),await async function(t,e=!0){await t.addShape("circle",new St,e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("color",(t=>new Jt(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("opacity",(t=>new Kt(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("outModes",(t=>new oe(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("size",(()=>new ne),e)}(t,!1),await t.refresh(e)}function re(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function ce(t,e,i,s){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.lineTo(s.x,s.y),t.closePath()}function he(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(0,0,e.width,e.height)}function le(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,0,0,e.width,e.height),t.globalAlpha=1)}function de(t,e){t.clearRect(0,0,e.width,e.height)}function ue(t){const{container:e,context:i,particle:s,delta:o,colorStyles:n,backgroundMask:a,composite:r,radius:c,opacity:h,shadow:l,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:0),f=Math.sin(p),m=Math.cos(p),v={a:m*(d.a??1),b:f*(d.b??1),c:-f*(d.c??1),d:m*(d.d??1)};i.setTransform(v.a,v.b,v.c,v.d,u.x,u.y),i.beginPath(),a&&(i.globalCompositeOperation=r);const g=s.shadowColor;l.enable&&g&&(i.shadowBlur=l.blur,i.shadowColor=$t(g),i.shadowOffsetX=l.offset.x,i.shadowOffsetY=l.offset.y),n.fill&&(i.fillStyle=n.fill);const y=s.strokeWidth??0;i.lineWidth=y,n.stroke&&(i.strokeStyle=n.stroke),pe(e,i,s,c,h,o),y>0&&i.stroke(),s.close&&i.closePath(),s.fill&&i.fill(),fe(e,i,s,c,h,o),i.globalCompositeOperation="source-over",i.setTransform(1,0,0,1,0,0)}function pe(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.draw(e,i,s,o,n,t.retina.pixelRatio)}function fe(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.afterEffect&&a.afterEffect(e,i,s,o,n,t.retina.pixelRatio)}function me(t,e,i){e.draw&&e.draw(t,i)}function ve(t,e,i,s){e.drawParticle&&e.drawParticle(t,i,s)}function ge(t,e,i){return{h:t.h,s:t.s,l:t.l+("darken"===e?-1:1)*i}}function ye(t,e,i){const s=e[i];void 0!==s&&(t[i]=(t[i]??1)*s)}class be{constructor(t){this.container=t,this._applyPostDrawUpdaters=t=>{for(const e of this._postDrawUpdaters)e.afterDraw&&e.afterDraw(t)},this._applyPreDrawUpdaters=(t,e,i,s,o,n)=>{for(const a of this._preDrawUpdaters){if(a.getColorStyles){const{fill:n,stroke:r}=a.getColorStyles(e,t,i,s);n&&(o.fill=n),r&&(o.stroke=r)}if(a.getTransformValues){const t=a.getTransformValues(e);for(const e in t)ye(n,t,e)}a.beforeDraw&&a.beforeDraw(e)}},this._applyResizePlugins=()=>{for(const t of this._resizePlugins)t.resize&&t.resize()},this._getPluginParticleColors=t=>{let e,i;for(const s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=At(s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=At(s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=()=>{const t=this.container.actualOptions.backgroundMask.cover,e=Et(t.color);if(e){const i={...e,a:t.opacity};this._coverColorStyle=$t(i,i.a)}},this._initStyle=()=>{const t=this.element,e=this.container.actualOptions;if(t){this._fullScreen?(this._originalStyle=I({},t.style),this._setFullScreenStyle()):this._resetOriginalStyle();for(const i in e.style){if(!i||!e.style)continue;const s=e.style[i];s&&t.style.setProperty(i,s,"important")}}},this._initTrail=async()=>{const t=this.container.actualOptions,e=t.particles.move.trail,i=e.fill;if(e.enable)if(i.color){const e=Et(i.color);if(!e)return;const s=t.particles.move.trail;this._trailFill={color:{...e},opacity:1/s.length}}else await new Promise(((t,s)=>{if(!i.image)return;const o=document.createElement("img");o.addEventListener("load",(()=>{this._trailFill={image:o,opacity:1/e.length},t()})),o.addEventListener("error",(t=>{s(t.error)})),o.src=i.image}))},this._paintBase=t=>{this.draw((e=>he(e,this.size,t)))},this._paintImage=(t,e)=>{this.draw((i=>le(i,this.size,t,e)))},this._repairStyle=()=>{const t=this.element;t&&(this._safeMutationObserver((t=>t.disconnect())),this._initStyle(),this.initBackground(),this._safeMutationObserver((e=>e.observe(t,{attributes:!0}))))},this._resetOriginalStyle=()=>{const t=this.element,e=this._originalStyle;if(!t||!e)return;const i=t.style;i.position=e.position,i.zIndex=e.zIndex,i.top=e.top,i.left=e.left,i.width=e.width,i.height=e.height},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{const t=this.element;if(!t)return;const e="important",i=t.style;i.setProperty("position","fixed",e),i.setProperty("z-index",this.container.actualOptions.fullScreen.zIndex.toString(10),e),i.setProperty("top","0",e),i.setProperty("left","0",e),i.setProperty("width","100%",e),i.setProperty("height","100%",e)},this.size={height:0,width:0},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){const t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>0&&i?i.color?this._paintBase($t(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):this.draw((t=>{de(t,this.size)}))}destroy(){if(this.stop(),this._generated){const t=this.element;t&&t.remove()}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){const e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;const i=t.getRadius();if(i<=0)return;const s=t.getFillColor(),o=t.getStrokeColor()??s;let[n,a]=this._getPluginParticleColors(t);n||(n=s),a||(a=o),(n||a)&&this.draw((s=>{const o=this.container,r=o.actualOptions,c=t.options.zIndex,h=(1-t.zIndexFactor)**c.opacityRate,l=t.bubble.opacity??t.opacity?.value??1,d=l*h,u=(t.strokeOpacity??l)*h,p={},f={fill:n?Gt(n,d):void 0};f.stroke=a?Gt(a,u):f.fill,this._applyPreDrawUpdaters(s,t,i,d,f,p),ue({container:o,context:s,particle:t,delta:e,colorStyles:f,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*(1-t.zIndexFactor)**c.sizeRate,opacity:d,shadow:t.options.shadow,transform:p}),this._applyPostDrawUpdaters(t)}))}drawParticlePlugin(t,e,i){this.draw((s=>ve(s,t,e,i)))}drawPlugin(t,e){this.draw((i=>me(i,t,e)))}async init(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=M((t=>{for(const e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()})),this.resize(),this._initStyle(),this._initCover();try{await this._initTrail()}catch(t){b().error(t)}this.initBackground(),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){const t=this.container.actualOptions.background,e=this.element;if(!e)return;const i=e.style;if(i){if(t.color){const e=Et(t.color);i.backgroundColor=e?$t(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){this._resizePlugins=[];for(const[,t]of this.container.plugins)t.resize&&this._resizePlugins.push(t),(t.particleFillColor||t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){this._preDrawUpdaters=[],this._postDrawUpdaters=[];for(const t of this.container.particles.updaters)t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles||t.getTransformValues||t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&i in t.dataset?"true"===t.dataset[i]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=I({},this.element.style),this.size.height=t.offsetHeight,this.size.width=t.offsetWidth,this._context=this.element.getContext("2d"),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.container.retina.init(),this.initBackground()}paint(){const t=this.container.actualOptions;this.draw((e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(de(e,this.size),this._paintBase(this._coverColorStyle)):this._paintBase()}))}resize(){if(!this.element)return!1;const t=this.container,e=t.retina.pixelRatio,i=t.canvas.size,s=this.element.offsetWidth*e,o=this.element.offsetHeight*e;if(o===i.height&&s===i.width&&o===this.element.height&&s===this.element.width)return!1;const n={...i};return this.element.width=i.width=this.element.offsetWidth*e,this.element.height=i.height=this.element.offsetHeight*e,this.container.started&&(this.resizeFactor={width:i.width/n.width,height:i.height/n.height}),!0}stop(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=void 0,this.draw((t=>de(t,this.size)))}async windowResize(){if(!this.element||!this.resize())return;const t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function _e(t,e,i,s,o){if(s){let s={passive:!0};Y(o)?s.capture=o:void 0!==o&&(s=o),t.addEventListener(e,i,s)}else{const s=o;t.removeEventListener(e,i,s)}}class we{constructor(t){this.container=t,this._doMouseTouchClick=t=>{const e=this.container,i=e.actualOptions;if(this._canPush){const t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=(new Date).getTime();H(i.interactivity.events.onClick.mode,(t=>this.container.handleClickMode(t)))}"touchend"===t.type&&setTimeout((()=>this._mouseTouchFinish()),500)},this._handleThemeChange=t=>{const e=t,i=this.container,s=i.options,o=s.defaultThemes,n=e.matches?o.dark:o.light,a=s.themes.find((t=>t.name===n));a&&a.default.auto&&i.loadTheme(n)},this._handleVisibilityChange=()=>{const t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document&&document.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.getAnimationStatus()?t.play(!0):t.draw(!0)))},this._handleWindowResize=async()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout),this._resizeTimeout=setTimeout((async()=>{const t=this.container.canvas;t&&await t.windowResize()}),1e3*this.container.actualOptions.interactivity.events.resize.delay)},this._manageInteractivityListeners=(t,e)=>{const i=this._handlers,n=this.container,a=n.actualOptions,u=n.interactivity.element;if(!u)return;const p=u,f=n.canvas.element;f&&(f.style.pointerEvents=p===f?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(_e(u,r,i.mouseMove,e),_e(u,c,i.touchStart,e),_e(u,l,i.touchMove,e),a.interactivity.events.onClick.enable?(_e(u,h,i.touchEndClick,e),_e(u,o,i.mouseUp,e),_e(u,s,i.mouseDown,e)):_e(u,h,i.touchEnd,e),_e(u,t,i.mouseLeave,e),_e(u,d,i.touchCancel,e))},this._manageListeners=t=>{const e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,o=i.canvas.element;let r=n;"window"===s?(i.interactivity.element=window,r=a):i.interactivity.element="parent"===s&&o?o.parentElement??o.parentNode:o,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(r,t),document&&_e(document,p,e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{const e=this._handlers,i=z("(prefers-color-scheme: dark)");i&&(void 0===i.addEventListener?void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange)):_e(i,"change",e.themeChange,t))},this._manageResize=t=>{const e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver)return void _e(window,u,e.resize,t);const s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver((async t=>{t.find((t=>t.target===s))&&await this._handleWindowResize()})),this._resizeObserver.observe(s))},this._mouseDown=()=>{const{interactivity:t}=this.container;if(!t)return;const{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{const e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let o=!1;const n=s.position;if(n&&i.interactivity.events.onClick.enable){for(const[,t]of e.plugins)if(t.clickPositionValid&&(o=t.clickPositionValid(n),o))break;o||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{const t=this.container.interactivity;if(!t)return;const e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=n,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{const e=this.container,i=e.actualOptions,s=e.interactivity,o=e.canvas.element;if(!s||!s.element)return;let n;if(s.mouse.inside=!0,t.type.startsWith("pointer")){this._canPush=!0;const e=t;if(s.element===window){if(o){const t=o.getBoundingClientRect();n={x:e.clientX-t.left,y:e.clientY-t.top}}}else if("parent"===i.interactivity.detectsOn){const t=e.target,i=e.currentTarget;if(t&&i&&o){const s=t.getBoundingClientRect(),a=i.getBoundingClientRect(),r=o.getBoundingClientRect();n={x:e.offsetX+2*s.left-(a.left+r.left),y:e.offsetY+2*s.top-(a.top+r.top)}}else n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY}}else e.target===o&&(n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY})}else if(this._canPush="touchmove"!==t.type,o){const e=t,i=e.touches[e.touches.length-1],s=o.getBoundingClientRect();n={x:i.clientX-(s.left??0),y:i.clientY-(s.top??0)}}const a=e.retina.pixelRatio;n&&(n.x*=a,n.y*=a),s.mouse.position=n,s.status=r},this._touchEnd=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.set(t.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}class xe{constructor(){this.value=""}static create(t,e){const i=new xe;return i.load(t),void 0!==e&&(j(e)||J(e)?i.load({value:e}):i.load(e)),i}load(t){void 0!==t?.value&&(this.value=t.value)}}class ze{constructor(){this.color=new xe,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=xe.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Me{constructor(){this.color=new xe,this.color.value="#fff",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=xe.create(this.color,t.color)),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Pe{constructor(){this.composite="destination-out",this.cover=new Me,this.enable=!1}load(t){if(t){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const e=t.cover,i=j(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class Se{constructor(){this.enable=!0,this.zIndex=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class Oe{constructor(){this.enable=!1,this.mode=[]}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}class ke{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type="circle"}get el(){return this.elementId}set el(t){this.elementId=t}get elementId(){return this.ids}set elementId(t){this.ids=t}get ids(){return H(this.selectors,(t=>t.replace("#","")))}set ids(t){this.selectors=H(t,(t=>`#${t}`))}load(t){if(!t)return;const e=t.ids??t.elementId??t.el;void 0!==e&&(this.ids=e),void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type)}}class Ce{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class Te{constructor(){this.enable=!1,this.mode=[],this.parallax=new Ce}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class Re{constructor(){this.delay=.5,this.enable=!0}load(t){void 0!==t&&(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class Ie{constructor(){this.onClick=new Oe,this.onDiv=new ke,this.onHover=new Te,this.resize=new Re}get onclick(){return this.onClick}set onclick(t){this.onClick=t}get ondiv(){return this.onDiv}set ondiv(t){this.onDiv=t}get onhover(){return this.onHover}set onhover(t){this.onHover=t}load(t){if(!t)return;this.onClick.load(t.onClick??t.onclick);const e=t.onDiv??t.ondiv;void 0!==e&&(this.onDiv=H(e,(t=>{const e=new ke;return e.load(t),e}))),this.onHover.load(t.onHover??t.onhover),Y(t.resize)?this.resize.enable=t.resize:this.resize.load(t.resize)}}class Ee{constructor(t,e){this._engine=t,this._container=e}load(t){if(!t)return;if(!this._container)return;const e=this._engine.plugins.interactors.get(this._container);if(e)for(const i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class De{constructor(t,e){this.detectsOn="window",this.events=new Ie,this.modes=new Ee(t,e)}get detect_on(){return this.detectsOn}set detect_on(t){this.detectsOn=t}load(t){if(!t)return;const e=t.detectsOn??t.detect_on;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class Le{load(t){t&&(t.position&&(this.position={x:t.position.x??50,y:t.position.y??50,mode:t.position.mode??"percent"}),t.options&&(this.options=I({},t.options)))}}class Ae{constructor(){this.maxWidth=1/0,this.options={},this.mode="canvas"}load(t){t&&(void 0!==t.maxWidth&&(this.maxWidth=t.maxWidth),void 0!==t.mode&&("screen"===t.mode?this.mode="screen":this.mode="canvas"),void 0!==t.options&&(this.options=I({},t.options)))}}class Fe{constructor(){this.auto=!1,this.mode="any",this.value=!1}load(t){t&&(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class Ve{constructor(){this.name="",this.default=new Fe}load(t){t&&(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=I({},t.options)))}}class Be{constructor(){this.count=0,this.enable=!1,this.offset=0,this.speed=1,this.delay=0,this.decay=0,this.sync=!0}load(t){t&&(void 0!==t.count&&(this.count=dt(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(this.offset=dt(t.offset)),void 0!==t.speed&&(this.speed=dt(t.speed)),void 0!==t.decay&&(this.decay=dt(t.decay)),void 0!==t.delay&&(this.delay=dt(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class He{constructor(){this.h=new Be,this.s=new Be,this.l=new Be}load(t){t&&(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class Ue extends xe{constructor(){super(),this.animation=new He}static create(t,e){const i=new Ue;return i.load(t),void 0!==e&&(j(e)||J(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),!t)return;const e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}class We{constructor(){this.speed=2}load(t){t&&void 0!==t.speed&&(this.speed=t.speed)}}class $e{constructor(){this.enable=!0,this.retries=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.retries&&(this.retries=t.retries))}}class Ge{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){t&&(void 0!==t.count&&(this.count=dt(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=dt(t.speed)),void 0!==t.decay&&(this.decay=dt(t.decay)),void 0!==t.delay&&(this.delay=dt(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class qe extends Ge{constructor(){super(),this.mode="auto",this.startValue="random"}load(t){super.load(t),t&&(void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.startValue&&(this.startValue=t.startValue))}}class Xe{constructor(){this.enable=!1,this.minimumValue=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue))}}class Ye{constructor(){this.random=new Xe,this.value=0}load(t){t&&(Y(t.random)?this.random.enable=t.random:this.random.load(t.random),void 0!==t.value&&(this.value=dt(t.value,this.random.enable?this.random.minimumValue:void 0)))}}class je extends Ye{constructor(){super(),this.animation=new Ge}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&this.animation.load(e)}}class Ne extends je{constructor(){super(),this.animation=new qe}load(t){if(super.load(t),!t)return;void 0!==(t.animation??t.anim)&&(this.value=dt(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class Ze extends Ye{constructor(){super(),this.random.minimumValue=.1,this.value=1}}class Qe{constructor(){this.horizontal=new Ze,this.vertical=new Ze}load(t){t&&(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class Je{constructor(){this.absorb=new We,this.bounce=new Qe,this.enable=!1,this.maxSpeed=50,this.mode="bounce",this.overlap=new $e}load(t){t&&(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=dt(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class Ke{constructor(){this.offset=0,this.value=90}load(t){t&&(void 0!==t.offset&&(this.offset=dt(t.offset)),void 0!==t.value&&(this.value=dt(t.value)))}}class ti{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}get rotateX(){return this.rotate.x}set rotateX(t){this.rotate.x=t}get rotateY(){return this.rotate.y}set rotateY(t){this.rotate.y=t}load(t){if(!t)return;void 0!==t.distance&&(this.distance=dt(t.distance)),void 0!==t.enable&&(this.enable=t.enable);const e=t.rotate?.x??t.rotateX;void 0!==e&&(this.rotate.x=e);const i=t.rotate?.y??t.rotateY;void 0!==i&&(this.rotate.y=i)}}class ei{constructor(){this.x=50,this.y=50,this.mode="percent",this.radius=0}load(t){t&&(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.radius&&(this.radius=t.radius))}}class ii{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=dt(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=dt(t.maxSpeed)))}}class si{constructor(){this.clamp=!0,this.delay=new Ye,this.enable=!1,this.options={}}load(t){t&&(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=I(this.options,t.options)))}}class oi{load(t){t&&(void 0!==t.color&&(this.color=xe.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image))}}class ni{constructor(){this.enable=!1,this.length=10,this.fill=new oi}get fillColor(){return this.fill.color}set fillColor(t){this.fill.load({color:t})}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0===t.fill&&void 0===t.fillColor||this.fill.load(t.fill||{color:t.fillColor}),void 0!==t.length&&(this.length=t.length))}}class ai{constructor(){this.default="out"}load(t){t&&(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class ri{constructor(){this.acceleration=0,this.enable=!1}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=dt(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=I({},t.position)))}}class ci{constructor(){this.angle=new Ke,this.attract=new ti,this.center=new ei,this.decay=0,this.distance={},this.direction="none",this.drift=0,this.enable=!1,this.gravity=new ii,this.path=new si,this.outModes=new ai,this.random=!1,this.size=!1,this.speed=2,this.spin=new ri,this.straight=!1,this.trail=new ni,this.vibrate=!1,this.warp=!1}get bounce(){return this.collisions}set bounce(t){this.collisions=t}get collisions(){return!1}set collisions(t){}get noise(){return this.path}set noise(t){this.path=t}get outMode(){return this.outModes.default}set outMode(t){this.outModes.default=t}get out_mode(){return this.outMode}set out_mode(t){this.outMode=t}load(t){if(!t)return;this.angle.load(N(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=dt(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=N(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=dt(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);const e=t.outModes??t.outMode??t.out_mode;void 0!==e&&(Q(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path??t.noise),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=dt(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class hi extends qe{constructor(){super(),this.destroy="none",this.speed=2}get opacity_min(){return this.minimumValue}set opacity_min(t){this.minimumValue=t}load(t){void 0!==t?.opacity_min&&void 0===t.minimumValue&&(t.minimumValue=t.opacity_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class li extends Ye{constructor(){super(),this.animation=new hi,this.random.minimumValue=.1,this.value=1}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(!t)return;super.load(t);const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=dt(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class di{constructor(){this.enable=!1,this.width=1920,this.height=1080}get area(){return this.width}set area(t){this.width=t}get factor(){return this.height}set factor(t){this.height=t}get value_area(){return this.area}set value_area(t){this.area=t}load(t){if(!t)return;void 0!==t.enable&&(this.enable=t.enable);const e=t.width??t.area??t.value_area;void 0!==e&&(this.width=e);const i=t.height??t.factor;void 0!==i&&(this.height=i)}}class ui{constructor(){this.density=new di,this.limit=0,this.value=0}get max(){return this.limit}set max(t){this.limit=t}load(t){if(!t)return;this.density.load(t.density);const e=t.limit??t.max;void 0!==e&&(this.limit=e),void 0!==t.value&&(this.value=t.value)}}class pi{constructor(){this.blur=0,this.color=new xe,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=xe.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}const fi="character",mi="char",vi="image",gi="images",yi="polygon",bi="star";class _i{constructor(){this.loadShape=(t,e,i,s)=>{if(!t)return;const o=J(t),n=o?[]:{},a=o!==J(this.options[e]),r=o!==J(this.options[i]);a&&(this.options[e]=n),r&&s&&(this.options[i]=n),this.options[e]=I(this.options[e]??n,t),this.options[i]&&!s||(this.options[i]=I(this.options[i]??n,t))},this.close=!0,this.fill=!0,this.options={},this.type="circle"}get character(){return this.options[fi]??this.options[mi]}set character(t){this.options[mi]=this.options[fi]=t}get custom(){return this.options}set custom(t){this.options=t}get image(){return this.options[vi]??this.options[gi]}set image(t){this.options[gi]=this.options[vi]=t}get images(){return this.image}set images(t){this.image=t}get polygon(){return this.options[yi]??this.options[bi]}set polygon(t){this.options[bi]=this.options[yi]=t}get stroke(){return[]}set stroke(t){}load(t){if(!t)return;const e=t.options??t.custom;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=I(this.options[t]??{},i))}this.loadShape(t.character,fi,mi,!0),this.loadShape(t.polygon,yi,bi,!1),this.loadShape(t.image??t.images,vi,gi,!0),void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class wi extends qe{constructor(){super(),this.destroy="none",this.speed=5}get size_min(){return this.minimumValue}set size_min(t){this.minimumValue=t}load(t){void 0!==t?.size_min&&void 0===t.minimumValue&&(t.minimumValue=t.size_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class xi extends Ye{constructor(){super(),this.animation=new wi,this.random.minimumValue=1,this.value=3}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=dt(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class zi{constructor(){this.width=0}load(t){t&&(void 0!==t.color&&(this.color=Ue.create(this.color,t.color)),void 0!==t.width&&(this.width=dt(t.width)),void 0!==t.opacity&&(this.opacity=dt(t.opacity)))}}class Mi extends Ye{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),t&&(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0!==t.velocityRate&&(this.velocityRate=t.velocityRate))}}class Pi{constructor(t,e){this._engine=t,this._container=e,this.bounce=new Qe,this.collisions=new Je,this.color=new Ue,this.color.value="#fff",this.groups={},this.move=new ci,this.number=new ui,this.opacity=new li,this.reduceDuplicates=!1,this.shadow=new pi,this.shape=new _i,this.size=new xi,this.stroke=new zi,this.zIndex=new Mi}load(t){if(!t)return;if(this.bounce.load(t.bounce),this.color.load(Ue.create(this.color,t.color)),void 0!==t.groups)for(const e in t.groups){const i=t.groups[e];void 0!==i&&(this.groups[e]=I(this.groups[e]??{},i))}this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex);const e=t.move?.collisions??t.move?.bounce;void 0!==e&&(this.collisions.enable=e),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=I({},t.interactivity));const i=t.stroke??t.shape?.stroke;if(i&&(this.stroke=H(i,(t=>{const e=new zi;return e.load(t),e}))),this._container){const e=this._engine.plugins.updaters.get(this._container);if(e)for(const i of e)i.loadOptions&&i.loadOptions(this,t);const i=this._engine.plugins.interactors.get(this._container);if(i)for(const e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function Si(t,...e){for(const i of e)t.load(i)}function Oi(t,e,...i){const s=new Pi(t,e);return Si(s,...i),s}class ki{constructor(t,e){this._findDefaultTheme=t=>this.themes.find((e=>e.default.value&&e.default.mode===t))??this.themes.find((t=>t.default.value&&"any"===t.default.mode)),this._importPreset=t=>{this.load(this._engine.plugins.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new ze,this.backgroundMask=new Pe,this.defaultThemes={},this.delay=0,this.fullScreen=new Se,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new De(t,e),this.manualParticles=[],this.particles=Oi(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}get backgroundMode(){return this.fullScreen}set backgroundMode(t){this.fullScreen.load(t)}get fps_limit(){return this.fpsLimit}set fps_limit(t){this.fpsLimit=t}get retina_detect(){return this.detectRetina}set retina_detect(t){this.detectRetina=t}load(t){if(!t)return;void 0!==t.preset&&H(t.preset,(t=>this._importPreset(t))),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.delay&&(this.delay=dt(t.delay));const e=t.detectRetina??t.retina_detect;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=dt(t.duration));const i=t.fpsLimit??t.fps_limit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);const s=t.fullScreen??t.backgroundMode;Y(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const e=new Le;return e.load(t),e}))),this.particles.load(t.particles),this.style=I(this.style,t.style),this._engine.plugins.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);const o=this._engine.plugins.interactors.get(this._container);if(o)for(const e of o)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(const e of t.responsive){const t=new Ae;t.load(e),this.responsive.push(t)}if(this.responsive.sort(((t,e)=>t.maxWidth-e.maxWidth)),void 0!==t.themes)for(const e of t.themes){const t=this.themes.find((t=>t.name===e.name));if(t)t.load(e);else{const t=new Ve;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme("dark")?.name,this.defaultThemes.light=this._findDefaultTheme("light")?.name}setResponsive(t,e,i){this.load(i);const s=this.responsive.find((i=>"screen"===i.mode&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t));return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){const e=this.themes.find((e=>e.name===t));e&&this.load(e.options)}else{const t=z("(prefers-color-scheme: dark)"),e=t&&t.matches,i=this._findDefaultTheme(e?"dark":"light");i&&this.load(i.options)}}}class Ci{constructor(t,e){this.container=e,this._engine=t,this._interactors=t.plugins.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[]}async externalInteract(t){for(const e of this._externalInteractors)e.isEnabled()&&await e.interact(t)}handleClickMode(t){for(const e of this._externalInteractors)e.handleClickMode&&e.handleClickMode(t)}init(){this._externalInteractors=[],this._particleInteractors=[];for(const t of this._interactors){switch(t.type){case"external":this._externalInteractors.push(t);break;case"particles":this._particleInteractors.push(t)}t.init()}}async particlesInteract(t,e){for(const i of this._externalInteractors)i.clear(t,e);for(const i of this._particleInteractors)i.isEnabled(t)&&await i.interact(t,e)}async reset(t){for(const e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(const e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}const Ti=t=>{if(!P(t.outMode,t.checkModes))return;const e=2*t.radius;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)};class Ri{constructor(t,e,i,s,o,n){this.container=i,this._calcPosition=(t,e,i,s=0)=>{for(const[,s]of t.plugins){const t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return m.create(t.x,t.y,i)}const o=wt({size:t.canvas.size,position:e}),n=m.create(o.x,o.y,i),a=this.getRadius(),r=this.options.move.outModes,c=e=>{Ti({outMode:e,checkModes:["bounce","bounce-horizontal"],coord:n.x,maxCoord:t.canvas.size.width,setCb:t=>n.x+=t,radius:a})},h=e=>{Ti({outMode:e,checkModes:["bounce","bounce-vertical"],coord:n.y,maxCoord:t.canvas.size.height,setCb:t=>n.y+=t,radius:a})};return c(r.left??r.default),c(r.right??r.default),h(r.top??r.default),h(r.bottom??r.default),this._checkOverlap(n,s)?this._calcPosition(t,void 0,i,s+1):n},this._calculateVelocity=()=>{const t=vt(this.direction).copy(),e=this.options.move;if("inside"===e.direction||"outside"===e.direction)return t;const i=Math.PI/180*ct(e.angle.value),s=Math.PI/180*ct(e.angle.offset),o={left:s-i/2,right:s+i/2};return e.straight||(t.angle+=rt(dt(o.left,o.right))),e.random&&"number"==typeof e.speed&&(t.length*=ot()),t},this._checkOverlap=(t,e=0)=>{const i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;const o=i.overlap;if(o.enable)return!1;const n=o.retries;if(n>=0&&e>n)throw new Error(`${f} particle is overlapping and can't be placed`);return!!this.container.particles.find((e=>ft(t,e.position)<s+e.getRadius()))},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;const e=this.roll.horizontal&&this.roll.vertical?2:1,i=this.roll.horizontal?Math.PI/2:0;return Math.floor(((this.roll.angle??0)+i)/(Math.PI/e))%2?this.backColor?this.backColor:this.roll.alter?ge(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{const e=this.container,i=ct(this.options.zIndex.value);this.position=this._calcPosition(e,t,nt(i,0,e.zLayers)),this.initialPosition=this.position.copy();const s=e.canvas.size;switch(this.moveCenter={...q(this.options.move.center,s),radius:this.options.move.center.radius??0,mode:this.options.move.center.mode??"percent"},this.direction=mt(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case"inside":this.outType="inside";break;case"outside":this.outType="outside"}this.offset=v.origin},this._loadShapeData=(t,e)=>{const i=t.options[this.shape];if(i)return I({close:t.close,fill:t.fill},U(i,this.id,e))},this._engine=t,this.init(e,s,o,n)}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;const e=this.container,i=this.pathGenerator;for(const[,i]of e.plugins)i.particleDestroyed&&i.particleDestroyed(this,t);for(const i of e.particles.updaters)i.particleDestroyed&&i.particleDestroyed(this,t);i&&i.reset(this)}draw(t){const e=this.container;for(const[,i]of e.plugins)e.canvas.drawParticlePlugin(i,this,t);e.canvas.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??jt(this.color))}getMass(){return this.getRadius()**2*Math.PI/2}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??jt(this.strokeColor))}init(t,e,i,s){const o=this.container,n=this._engine;this.id=t,this.group=s,this.fill=!0,this.pathRotation=!1,this.close=!0,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType="normal",this.ignoresResizeRatio=!0;const a=o.retina.pixelRatio,r=o.actualOptions,c=Oi(this._engine,o,r.particles),h=c.shape.type,{reduceDuplicates:l}=c;this.shape=U(h,this.id,l);const d=c.shape;if(i&&i.shape&&i.shape.type){const t=U(i.shape.type,this.id,l);t&&(this.shape=t,d.load(i.shape))}this.shapeData=this._loadShapeData(d,l),c.load(i);const u=this.shapeData;u&&c.load(u.particles);const p=new De(n,o);p.load(o.actualOptions.interactivity),p.load(c.interactivity),this.interactivity=p,this.fill=u?.fill??c.shape.fill,this.close=u?.close??c.shape.close,this.options=c;const f=this.options.move.path;this.pathDelay=1e3*ut(f.delay),f.generator&&(this.pathGenerator=this._engine.plugins.getPathGenerator(f.generator),this.pathGenerator&&o.addPath(f.generator,this.pathGenerator)&&this.pathGenerator.init(o)),o.retina.initParticle(this),this.size=$(this.options.size,a),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=1-ct(this.options.move.decay);const m=o.particles;m.needsSort=m.needsSort||m.lastZIndex<this.position.z,m.lastZIndex=this.position.z,this.zIndexFactor=this.position.z/o.zLayers,this.sides=24;let v=o.drawers.get(this.shape);v||(v=this._engine.plugins.getShapeDrawer(this.shape),v&&o.drawers.set(this.shape,v)),v&&v.loadShape&&v.loadShape(this);const g=v?.getSidesCount;g&&(this.sides=g(this)),this.spawning=!1,this.shadowColor=Et(this.options.shadow.color);for(const t of o.particles.updaters)t.init(this);for(const t of o.particles.movers)t.init&&t.init(this);v&&v.particleInit&&v.particleInit(o,this);for(const[,t]of o.plugins)t.particleCreated&&t.particleCreated(this)}isInsideCanvas(){const t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(const t of this.container.particles.updaters)t.reset&&t.reset(this)}}class Ii{constructor(t,e){this.position=t,this.particle=e}}class Ei{constructor(t,e){this.position={x:t,y:e}}}class Di extends Ei{constructor(t,e,i,s){super(t,e),this.size={height:s,width:i}}contains(t){const e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){t instanceof Li&&t.intersects(this);const e=this.size.width,i=this.size.height,s=this.position,o=t.position,n=t instanceof Di?t.size:{width:0,height:0},a=n.width,r=n.height;return o.x<s.x+e&&o.x+a>s.x&&o.y<s.y+i&&o.y+r>s.y}}class Li extends Ei{constructor(t,e,i){super(t,e),this.radius=i}contains(t){return ft(t,this.position)<=this.radius}intersects(t){const e=this.position,i=t.position,s=Math.abs(i.x-e.x),o=Math.abs(i.y-e.y),n=this.radius;if(t instanceof Li){return n+t.radius>Math.sqrt(s**2+o**2)}if(t instanceof Di){const{width:e,height:i}=t.size;return Math.pow(s-e,2)+Math.pow(o-i,2)<=n**2||s<=n+e&&o<=n+i||s<=e||o<=i}return!1}}class Ai{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{const{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:o}=this;for(let n=0;n<4;n++)this._subs.push(new Ai(new Di(t+i/2*(n%2),e+s/2*(Math.round(n/2)-n%2),i/2,s/2),o));this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some((e=>e.insert(t)))))}query(t,e,i){const s=i||[];if(!t.intersects(this.rectangle))return[];for(const i of this._points)!t.contains(i.position)&&ft(t.position,i.position)>i.particle.getRadius()&&(!e||e(i.particle))||s.push(i.particle);if(this._divided)for(const i of this._subs)i.query(t,e,s);return s}queryCircle(t,e,i){return this.query(new Li(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new Di(t.x,t.y,e.width,e.height),i)}}const Fi=t=>new Di(-t.width/4,-t.height/4,3*t.width/2,3*t.height/2);class Vi{constructor(t,e){this._applyDensity=(t,e,i)=>{if(!t.number.density?.enable)return;const s=t.number,o=this._initDensityFactor(s.density),n=s.value,a=s.limit>0?s.limit:n,r=Math.min(n,a)*o+e,c=Math.min(this.count,this.filter((t=>t.group===i)).length);this.limit=s.limit*o,c<r?this.push(Math.abs(r-c),void 0,t,i):c>r&&this.removeQuantity(c-r,i)},this._initDensityFactor=t=>{const e=this._container;if(!e.canvas.element||!t.enable)return 1;const i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.factor*s**2*t.area)},this._pushParticle=(t,e,i,s)=>{try{let o=this.pool.pop();o?o.init(this._nextId,t,e,i):o=new Ri(this._engine,this._nextId,this._container,t,e,i);let n=!0;if(s&&(n=s(o)),!n)return;return this._array.push(o),this._zArray.push(o),this._nextId++,this._engine.dispatchEvent("particleAdded",{container:this._container,data:{particle:o}}),o}catch(t){return void b().warning(`${f} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{const s=this._array[t];if(!s||s.group!==e)return!1;s.destroy(i);const o=this._zArray.indexOf(s);return this._array.splice(t,1),this._zArray.splice(o,1),this.pool.push(s),this._engine.dispatchEvent("particleRemoved",{container:this._container,data:{particle:s}}),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this.pool=[],this.limit=0,this.needsSort=!1,this.lastZIndex=0,this._interactionManager=new Ci(t,e);const i=e.canvas.size;this.quadTree=new Ai(Fi(i),4),this.movers=this._engine.plugins.getMovers(e,!0),this.updaters=this._engine.plugins.getUpdaters(e,!0)}get count(){return this._array.length}addManualParticles(){const t=this._container,e=t.actualOptions;for(const i of e.manualParticles)this.addParticle(i.position?q(i.position,t.canvas.size):void 0,i.options)}addParticle(t,e,i,s){const o=this._container.actualOptions.particles.number.limit;if(o>0){const t=this.count+1-o;t>0&&this.removeQuantity(t)}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[]}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}async draw(t){const e=this._container;e.canvas.clear(),await this.update(t);for(const[,i]of e.plugins)e.canvas.drawPlugin(i,t);for(const e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}handleClickMode(t){this._interactionManager.handleClickMode(t)}init(){const t=this._container,e=t.actualOptions;this.lastZIndex=0,this.needsSort=!1;let i=!1;this.updaters=this._engine.plugins.getUpdaters(t,!0),this._interactionManager.init();for(const[,e]of t.plugins)if(void 0!==e.particlesInitialization&&(i=e.particlesInitialization()),i)break;this._interactionManager.init();for(const[,e]of t.pathGenerators)e.init(t);if(this.addManualParticles(),!i){for(const t in e.particles.groups){const i=e.particles.groups[t];for(let s=this.count,o=0;o<i.number?.value&&s<e.particles.number.value;s++,o++)this.addParticle(void 0,i,t)}for(let t=this.count;t<e.particles.number.value;t++)this.addParticle()}}push(t,e,i,s){this.pushing=!0;for(let o=0;o<t;o++)this.addParticle(e?.position,i,s);this.pushing=!1}async redraw(){this.clear(),this.init(),await this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=1,i,s){if(t<0||t>this.count)return;let o=0;for(let n=t;o<e&&n<this.count;n++)this._removeParticle(n--,i,s)&&o++}removeQuantity(t,e){this.removeAt(0,t,e)}setDensity(){const t=this._container.actualOptions,e=t.particles.groups;for(const t in e)this._applyDensity(e[t],0,t);this._applyDensity(t.particles,t.manualParticles.length)}async update(t){const e=this._container,i=new Set;this.quadTree=new Ai(Fi(e.canvas.size),4);for(const[,t]of e.pathGenerators)t.update();for(const[,i]of e.plugins)i.update&&i.update(t);for(const s of this._array){const o=e.canvas.resizeFactor;o&&!s.ignoresResizeRatio&&(s.position.x*=o.width,s.position.y*=o.height,s.initialPosition.x*=o.width,s.initialPosition.y*=o.height),s.ignoresResizeRatio=!1,await this._interactionManager.reset(s);for(const[,e]of this._container.plugins){if(s.destroyed)break;e.particleUpdate&&e.particleUpdate(s,t)}for(const e of this.movers)e.isEnabled(s)&&e.move(s,t);s.destroyed?i.add(s):this.quadTree.insert(new Ii(s.getPosition(),s))}if(i.size){const t=t=>!i.has(t);this._array=this.filter(t),this._zArray=this._zArray.filter(t),this.pool.push(...i)}await this._interactionManager.externalInteract(t);for(const e of this._array){for(const i of this.updaters)i.update(e,t);e.destroyed||e.spawning||await this._interactionManager.particlesInteract(e,t)}if(delete e.canvas.resizeFactor,this.needsSort){const t=this._zArray;t.sort(((t,e)=>e.position.z-t.position.z||t.id-e.id)),this.lastZIndex=t[t.length-1].position.z,this.needsSort=!1}}}class Bi{constructor(t){this.container=t,this.pixelRatio=1,this.reduceFactor=1}init(){const t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||w()?1:window.devicePixelRatio,this.reduceFactor=1;const i=this.pixelRatio;if(t.canvas.element){const e=t.canvas.element;t.canvas.size.width=e.offsetWidth*i,t.canvas.size.height=e.offsetHeight*i}const s=e.particles,o=s.move;this.attractDistance=ct(o.attract.distance)*i,this.maxSpeed=ct(o.gravity.maxSpeed)*i,this.sizeAnimationSpeed=ct(s.size.animation.speed)*i}initParticle(t){const e=t.options,i=this.pixelRatio,s=e.move,o=s.distance,n=t.retina;n.attractDistance=ct(s.attract.distance)*i,n.moveDrift=ct(s.drift)*i,n.moveSpeed=ct(s.speed)*i,n.sizeAnimationSpeed=ct(e.size.animation.speed)*i;const a=n.maxDistance;a.horizontal=void 0!==o.horizontal?o.horizontal*i:void 0,a.vertical=void 0!==o.vertical?o.vertical*i:void 0,n.maxSpeed=ct(s.gravity.maxSpeed)*i}}function Hi(t){return t&&!t.destroyed}function Ui(t,e,...i){const s=new ki(t,e);return Si(s,...i),s}const Wi={generate:t=>t.velocity,init:()=>{},update:()=>{},reset:()=>{}};class $i{constructor(t,e,i){this.id=e,this._intersectionManager=t=>{if(Hi(this)&&this.actualOptions.pauseOnOutsideViewport)for(const e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play:this.pause)()},this._nextFrame=async t=>{try{if(!this.smooth&&void 0!==this.lastFrameTime&&t<this.lastFrameTime+1e3/this.fpsLimit)return void this.draw(!1);this.lastFrameTime??=t;const e=function(t,e=60,i=!1){return{value:t,factor:i?60/e:60*t/1e3}}(t-this.lastFrameTime,this.fpsLimit,this.smooth);if(this.addLifeTime(e.value),this.lastFrameTime=t,e.value>1e3)return void this.draw(!1);if(await this.particles.draw(e),!this.alive())return void this.destroy();this.getAnimationStatus()&&this.draw(!1)}catch(t){b().error(`${f} in animation loop`,t)}},this._engine=t,this.fpsLimit=120,this.smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this.lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new Bi(this),this.canvas=new be(this),this.particles=new Vi(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.drawers=new Map,this._options=Ui(this._engine,this),this.actualOptions=Ui(this._engine,this),this._eventListeners=new we(this),"undefined"!=typeof IntersectionObserver&&IntersectionObserver&&(this._intersectionObserver=new IntersectionObserver((t=>this._intersectionManager(t)))),this._engine.dispatchEvent("containerBuilt",{container:this})}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!Hi(this))return;const e=this.interactivity.element;if(!e)return;const i=(e,i,s)=>{if(!Hi(this))return;const o=this.retina.pixelRatio,n={x:i.x*o,y:i.y*o},a=this.particles.quadTree.queryCircle(n,s*o);t(e,a)};let s=!1,o=!1;e.addEventListener("click",(t=>{if(!Hi(this))return;const e=t,s={x:e.offsetX||e.clientX,y:e.offsetY||e.clientY};i(t,s,1)})),e.addEventListener("touchstart",(()=>{Hi(this)&&(s=!0,o=!1)})),e.addEventListener("touchmove",(()=>{Hi(this)&&(o=!0)})),e.addEventListener("touchend",(t=>{if(Hi(this)){if(s&&!o){const e=t;let s=e.touches[e.touches.length-1];if(!s&&(s=e.changedTouches[e.changedTouches.length-1],!s))return;const o=this.canvas.element,n=o?o.getBoundingClientRect():void 0,a={x:s.clientX-(n?n.left:0),y:s.clientY-(n?n.top:0)};i(t,a,Math.max(s.radiusX,s.radiusY))}s=!1,o=!1}})),e.addEventListener("touchcancel",(()=>{Hi(this)&&(s=!1,o=!1)}))}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!Hi(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e??Wi),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}destroy(){if(!Hi(this))return;this.stop(),this.particles.destroy(),this.canvas.destroy();for(const[,t]of this.drawers)t.destroy&&t.destroy(this);for(const t of this.drawers.keys())this.drawers.delete(t);this._engine.plugins.destroy(this),this.destroyed=!0;const t=this._engine.dom(),e=t.findIndex((t=>t===this));e>=0&&t.splice(e,1),this._engine.dispatchEvent("containerDestroyed",{container:this})}draw(t){if(!Hi(this))return;let e=t;this._drawAnimationFrame=requestAnimationFrame((async t=>{e&&(this.lastFrameTime=void 0,e=!1),await this._nextFrame(t)}))}async export(t,e={}){for(const[,i]of this.plugins){if(!i.export)continue;const s=await i.export(t,e);if(s.supported)return s.blob}b().error(`${f} - Export plugin with type ${t} not found`)}getAnimationStatus(){return!this._paused&&!this.pageHidden&&Hi(this)}handleClickMode(t){if(Hi(this)){this.particles.handleClickMode(t);for(const[,e]of this.plugins)e.handleClickMode&&e.handleClickMode(t)}}async init(){if(!Hi(this))return;const t=this._engine.plugins.getSupportedShapes();for(const e of t){const t=this._engine.plugins.getShapeDrawer(e);t&&this.drawers.set(e,t)}this._options=Ui(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=Ui(this._engine,this,this._options);const e=this._engine.plugins.getAvailablePlugins(this);for(const[t,i]of e)this.plugins.set(t,i);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize(),this.zLayers=this.actualOptions.zLayers,this._duration=1e3*ct(this.actualOptions.duration),this._delay=1e3*ct(this.actualOptions.delay),this._lifeTime=0,this.fpsLimit=this.actualOptions.fpsLimit>0?this.actualOptions.fpsLimit:120,this.smooth=this.actualOptions.smooth;for(const[,t]of this.drawers)t.init&&await t.init(this);for(const[,t]of this.plugins)t.init&&await t.init();this._engine.dispatchEvent("containerInit",{container:this}),this.particles.init(),this.particles.setDensity();for(const[,t]of this.plugins)t.particlesSetup&&t.particlesSetup();this._engine.dispatchEvent("particlesSetup",{container:this})}async loadTheme(t){Hi(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(Hi(this)&&(void 0!==this._drawAnimationFrame&&(cancelAnimationFrame(this._drawAnimationFrame),delete this._drawAnimationFrame),!this._paused)){for(const[,t]of this.plugins)t.pause&&t.pause();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent("containerPaused",{container:this})}}play(t){if(!Hi(this))return;const e=this._paused||t;if(!this._firstStart||this.actualOptions.autoPlay){if(this._paused&&(this._paused=!1),e)for(const[,t]of this.plugins)t.play&&t.play();this._engine.dispatchEvent("containerPlay",{container:this}),this.draw(e||!1)}else this._firstStart=!1}async refresh(){if(Hi(this))return this.stop(),this.start()}async reset(){if(Hi(this))return this._initialSourceOptions=void 0,this._options=Ui(this._engine,this),this.actualOptions=Ui(this._engine,this,this._options),this.refresh()}setNoise(t,e,i){Hi(this)&&this.setPath(t,e,i)}setPath(t,e,i){if(!t||!Hi(this))return;const s={...Wi};if(Z(t))s.generate=t,e&&(s.init=e),i&&(s.update=i);else{const e=s;s.generate=t.generate||e.generate,s.init=t.init||e.init,s.update=t.update||e.update}this.addPath("default",s,!0)}async start(){Hi(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise((t=>{this._delayTimeout=setTimeout((async()=>{this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element);for(const[,t]of this.plugins)t.start&&await t.start();this._engine.dispatchEvent("containerStarted",{container:this}),this.play(),t()}),this._delay)})))}stop(){if(Hi(this)&&this.started){this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element);for(const[,t]of this.plugins)t.stop&&t.stop();for(const t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent("containerStopped",{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];const t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this.responsiveMaxWidth!==t&&(this.responsiveMaxWidth=t,!0)}}class Gi{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){const i=this._listeners.get(t);i&&i.forEach((t=>t(e)))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){const i=this._listeners.get(t);if(!i)return;const s=i.length,o=i.indexOf(e);o<0||(1===s?this._listeners.delete(t):i.splice(o,1))}}function qi(t,e,i,s=!1){let o=e.get(t);return o&&!s||(o=[...i.values()].map((e=>e(t))),e.set(t,o)),o}class Xi{constructor(t){this._engine=t,this.plugins=[],this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.drawers=new Map,this.pathGenerators=new Map}addInteractor(t,e){this._initializers.interactors.set(t,e)}addParticleMover(t,e){this._initializers.movers.set(t,e)}addParticleUpdater(t,e){this._initializers.updaters.set(t,e)}addPathGenerator(t,e){!this.getPathGenerator(t)&&this.pathGenerators.set(t,e)}addPlugin(t){!this.getPlugin(t.id)&&this.plugins.push(t)}addPreset(t,e,i=!1){(i||!this.getPreset(t))&&this.presets.set(t,e)}addShapeDrawer(t,e){H(t,(t=>{!this.getShapeDrawer(t)&&this.drawers.set(t,e)}))}destroy(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}getAvailablePlugins(t){const e=new Map;for(const i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,i.getPlugin(t));return e}getInteractors(t,e=!1){return qi(t,this.interactors,this._initializers.interactors,e)}getMovers(t,e=!1){return qi(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find((e=>e.id===t))}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.drawers.get(t)}getSupportedShapes(){return this.drawers.keys()}getUpdaters(t,e=!1){return qi(t,this.updaters,this._initializers.updaters,e)}loadOptions(t,e){for(const i of this.plugins)i.loadOptions(t,e)}loadParticlesOptions(t,e,...i){const s=this.updaters.get(t);if(s)for(const t of s)t.loadOptions&&t.loadOptions(e,...i)}}class Yi{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new Gi,this._initialized=!1,this.plugins=new Xi(this)}get configs(){const t={};for(const[e,i]of this._configs)t[e]=i;return t}get version(){return"2.12.0"}addConfig(t,e){j(t)?e&&(e.name=t,this._configs.set(t,e)):this._configs.set(t.name??"default",t)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this.plugins.addInteractor(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this.plugins.addParticleMover(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this.plugins.addParticleUpdater(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.plugins.addPathGenerator(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.plugins.addPlugin(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){this.plugins.addPreset(t,e,i),await this.refresh(s)}async addShape(t,e,i,s,o,n=!0){let a,r,c,h,l=n;Y(i)?(l=i,r=void 0):r=i,Y(s)?(l=s,c=void 0):c=s,Y(o)?(l=o,h=void 0):h=o,a=Z(e)?{afterEffect:c,destroy:h,draw:e,init:r}:e,this.plugins.addShapeDrawer(t,a),await this.refresh(l)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this._domArray}domItem(t){const e=this.dom(),i=e[t];if(i&&!i.destroyed)return i;e.splice(t,1)}init(){this._initialized||(this._initialized=!0)}async load(t,e){return this.loadFromArray(t,e)}async loadFromArray(t,e,i){let s;return!function(t){return!!((e=t).id||e.element||e.url||e.options);var e}(t)?(s={},j(t)?s.id=t:s.options=t,N(e)?s.index=e:s.options=e??s.options,s.index=i??s.index):s=t,this._loadParams(s)}async loadJSON(t,e,i){let s,o;return N(e)||void 0===e?s=t:(o=t,s=e),this._loadParams({id:o,url:s,index:i})}async refresh(t=!0){t&&this.dom().forEach((t=>t.refresh()))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}async set(t,e,i,s){const o={index:s};return j(t)?o.id=t:o.element=t,e instanceof HTMLElement?o.element=e:o.options=e,N(i)?o.index=i:o.options=i??o.options,this._loadParams(o)}async setJSON(t,e,i,s){const o={};return t instanceof HTMLElement?(o.element=t,o.url=e,o.index=i):(o.id=t,o.element=e,o.url=i,o.index=s),this._loadParams(o)}setOnClickHandler(t){const e=this.dom();if(!e.length)throw new Error(`${f} can only set click handlers after calling tsParticles.load()`);for(const i of e)i.addClickHandler(t)}async _loadParams(t){const e=t.id??`tsparticles${Math.floor(1e4*ot())}`,{index:s,url:o}=t,n=o?await async function(t){const e=U(t.url,t.index);if(!e)return t.fallback;const i=await fetch(e);return i.ok?i.json():(b().error(`${f} ${i.status} while retrieving config file`),t.fallback)}({fallback:t.options,url:o,index:s}):t.options;let a=t.element??document.getElementById(e);a||(a=document.createElement("div"),a.id=e,document.body.append(a));const r=U(n,s),c=this.dom(),h=c.findIndex((t=>t.id===e));if(h>=0){const t=this.domItem(h);t&&!t.destroyed&&(t.destroy(),c.splice(h,1))}let l;if("canvas"===a.tagName.toLowerCase())l=a,l.dataset[i]="false";else{const t=a.getElementsByTagName("canvas");t.length?(l=t[0],l.dataset[i]="false"):(l=document.createElement("canvas"),l.dataset[i]="true",a.appendChild(l))}l.style.width||(l.style.width="100%"),l.style.height||(l.style.height="100%");const d=new $i(this,e,r);return h>=0?c.splice(h,0,d):c.push(d),d.canvas.loadCanvas(l),await d.start(),d}}class ji{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return Ht(e)}handleRangeColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return Ht({h:ct(e.h),l:ct(e.l),s:ct(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;const e=/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?Ut({a:e.length>4?zt(e[5]):1,h:parseInt(e[1],10),l:parseInt(e[3],10),s:parseInt(e[2],10)}):void 0}}class Ni{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return{r:ct(e.r),g:ct(e.g),b:ct(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;const e=/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?{a:e.length>4?zt(e[5]):1,b:parseInt(e[3],10),g:parseInt(e[2],10),r:parseInt(e[1],10)}:void 0}}class Zi{constructor(t){this.container=t,this.type="external"}}class Qi{constructor(t){this.container=t,this.type="particles"}}const Ji=function(){const t=new Ni,e=new ji;Tt(t),Tt(e);const i=new Yi;return i.init(),i}();return w()||(window.tsParticles=Ji),ae(Ji),e})()));