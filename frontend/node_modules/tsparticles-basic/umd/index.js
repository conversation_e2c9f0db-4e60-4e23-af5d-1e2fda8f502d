(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "tsparticles-move-base", "tsparticles-shape-circle", "tsparticles-updater-color", "tsparticles-updater-opacity", "tsparticles-updater-out-modes", "tsparticles-updater-size"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadBasic = void 0;
    const tsparticles_move_base_1 = require("tsparticles-move-base");
    const tsparticles_shape_circle_1 = require("tsparticles-shape-circle");
    const tsparticles_updater_color_1 = require("tsparticles-updater-color");
    const tsparticles_updater_opacity_1 = require("tsparticles-updater-opacity");
    const tsparticles_updater_out_modes_1 = require("tsparticles-updater-out-modes");
    const tsparticles_updater_size_1 = require("tsparticles-updater-size");
    async function loadBasic(engine, refresh = true) {
        await (0, tsparticles_move_base_1.loadBaseMover)(engine, false);
        await (0, tsparticles_shape_circle_1.loadCircleShape)(engine, false);
        await (0, tsparticles_updater_color_1.loadColorUpdater)(engine, false);
        await (0, tsparticles_updater_opacity_1.loadOpacityUpdater)(engine, false);
        await (0, tsparticles_updater_out_modes_1.loadOutModesUpdater)(engine, false);
        await (0, tsparticles_updater_size_1.loadSizeUpdater)(engine, false);
        await engine.refresh(refresh);
    }
    exports.loadBasic = loadBasic;
});
