import { getRangeValue } from "../Utils/NumberUtils";
import { isSsr } from "../Utils/Utils";
export class Retina {
    constructor(container) {
        this.container = container;
        this.pixelRatio = 1;
        this.reduceFactor = 1;
    }
    init() {
        const container = this.container, options = container.actualOptions;
        this.pixelRatio = !options.detectRetina || isSsr() ? 1 : window.devicePixelRatio;
        this.reduceFactor = 1;
        const ratio = this.pixelRatio;
        if (container.canvas.element) {
            const element = container.canvas.element;
            container.canvas.size.width = element.offsetWidth * ratio;
            container.canvas.size.height = element.offsetHeight * ratio;
        }
        const particles = options.particles, moveOptions = particles.move;
        this.attractDistance = getRangeValue(moveOptions.attract.distance) * ratio;
        this.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;
        this.sizeAnimationSpeed = getRangeValue(particles.size.animation.speed) * ratio;
    }
    initParticle(particle) {
        const options = particle.options, ratio = this.pixelRatio, moveOptions = options.move, moveDistance = moveOptions.distance, props = particle.retina;
        props.attractDistance = getRangeValue(moveOptions.attract.distance) * ratio;
        props.moveDrift = getRangeValue(moveOptions.drift) * ratio;
        props.moveSpeed = getRangeValue(moveOptions.speed) * ratio;
        props.sizeAnimationSpeed = getRangeValue(options.size.animation.speed) * ratio;
        const maxDistance = props.maxDistance;
        maxDistance.horizontal = moveDistance.horizontal !== undefined ? moveDistance.horizontal * ratio : undefined;
        maxDistance.vertical = moveDistance.vertical !== undefined ? moveDistance.vertical * ratio : undefined;
        props.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;
    }
}
