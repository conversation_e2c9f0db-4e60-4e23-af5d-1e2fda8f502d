---
interface Props {
  article: {
    id: string;
    title: string;
    excerpt: string;
    slug: string;
    publish_date: string;
    featured_image: string;
    category: {
      name: string;
      slug: string;
    };
    author: {
      name: string;
      avatar: string;
    };
  };
  featured?: boolean;
  size?: 'small' | 'large';
}

const { article, featured = false, size = 'large' } = Astro.props;
const { title, excerpt, slug, publish_date, featured_image, category, author } = article;

const formattedDate = new Date(publish_date).toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'short',
  day: 'numeric'
});

const imageClass = {
  'small': 'h-40 rounded-t-xl',
  'large': featured ? 'h-72 md:h-80 lg:h-96' : 'h-56 rounded-t-xl',
}[size];

const cardClasses = featured 
  ? 'ghibli-card h-full ghibli-glow-enhanced bg-dark-surface rounded-xl shadow-lg border border-dark-border'
  : 'ghibli-card h-full bg-dark-surface rounded-xl shadow-lg border border-dark-border transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ghibli-glow-enhanced';
---

<article class={cardClasses}>
  <a href={`/article/${slug}`} class="block h-full">
    <div class={featured ? '' : 'h-full flex flex-col'}>
      <div class="relative">
        <img
          src={featured_image}
          alt={title}
          class={`w-full object-cover ${imageClass}`}
        />
        <div class="absolute top-4 left-4">
          {category && (
            <a
              href={`/category/${category.slug}`}
              class="inline-block px-3 py-1 text-xs font-medium text-dark-on-surface bg-brand-blue/20 rounded-full hover:bg-brand-blue/40 transition-colors shadow-md"
            >
              {category.name}
            </a>
          )}
        </div>
        <div class="absolute inset-0 bg-gradient-to-t from-dark-bg/70 via-transparent to-transparent"></div>
      </div>
      <div class={`flex-1 ${featured ? 'p-6 md:p-8 bg-white rounded-b-xl' : 'p-6'}`}>
        <div class="flex items-center mb-3 text-sm text-dark-on-surface/70">
          <span>{formattedDate}</span>
        </div>
        <h3 class="text-xl font-ghibliHeading font-bold text-dark-on-surface mb-3 line-clamp-2 hover:text-brand-blue transition-colors">
          {title}
        </h3>
        <p class="text-dark-on-surface/80 mb-5 line-clamp-2">
          {excerpt}
        </p>
        <div class="flex items-center">
          {author && (
            <>
              <img
                src={author.avatar}
                alt={author.name}
                class="w-8 h-8 rounded-full border-2 border-brand-blue/50"
              />
              <span class="ml-2 text-sm font-medium text-dark-on-surface/90">{author.name}</span>
            </>
          )}
          {featured && (
            <span class="ml-auto text-brand-blue font-medium text-sm flex items-center group">
              Read more
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </span>
          )}
        </div>
      </div>
    </div>
  </a>
</article>  