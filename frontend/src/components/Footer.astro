---
import { siteConfig } from '../config/site';
const currentYear = new Date().getFullYear();
---

<footer class="bg-dark-bg text-dark-on-surface py-12 sm:py-16 relative z-10 overflow-hidden ghibli-dust ghibli-glow-enhanced">
  <!-- Ghibli-inspired decorative elements -->
  <div class="absolute top-0 left-0 right-0 h-6 bg-dark-bg opacity-80 z-0"></div>
  <div class="absolute bottom-0 left-0 w-24 h-24 bg-dark-surface/20 rounded-full blur-xl"></div>
  <div class="absolute top-1/3 right-10 w-32 h-32 bg-brand-blue/10 rounded-full blur-xl"></div>
  
  <div class={`container mx-auto px-3 sm:px-4 ${siteConfig.containerWidth} relative z-10`}>
    <!-- Main content in two rows -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <!-- About Section -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            {siteConfig.name}
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <p class="text-dark-on-surface/80 mb-4">{siteConfig.description}</p>
        <p class="text-dark-on-surface/70 text-sm">
          Smart, honest product reviews and comparisons to help you make informed purchasing decisions.
        </p>
      </div>
      
      <!-- First Row - First Column (Quick Links) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Quick Links
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <ul class="space-y-3">
          {siteConfig.footerLinks.map(link => (
            <li><a href={link.href} class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">{link.title}</a></li>
          ))}
        </ul>
      </div>
      
      <!-- First Row - Second Column (Featured Brands) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Featured Brands
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <ul class="space-y-3">
          <li><a href="/articles?brand=Apple" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Apple</a></li>
          <li><a href="/articles?brand=Samsung" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Samsung</a></li>
          <li><a href="/articles?brand=Sony" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Sony</a></li>
          <li><a href="/brands" class="text-brand-blue hover:text-brand-blue/80 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Brands →</a></li>
        </ul>
      </div>
      
      <!-- First Row - Third Column (Subscribe) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Subscribe
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <p class="text-dark-on-surface/80 mb-4">Get the latest reviews and deals delivered straight to your inbox.</p>
        <form class="flex">
          <input 
            type="email" 
            placeholder="Your email" 
            class="flex-grow px-4 py-2 rounded-l-full focus:outline-none focus:ring-2 focus:ring-brand-blue text-dark-bg bg-dark-on-surface/90"
            required
          />
          <button 
            type="submit" 
            class="bg-brand-blue hover:bg-brand-blue/90 text-white px-5 py-2 rounded-r-full font-medium transition-all duration-300"
          >
            Subscribe
          </button>
        </form>
      </div>
    </div>

    <!-- Second row content -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mt-10 pt-8 border-t border-dark-border">
      <!-- Spacing to maintain alignment -->
      <div class="md:col-span-1">
        <!-- Empty -->
      </div>
      
      <!-- Second Row - First Column (Popular Categories) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Popular Categories
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <ul class="space-y-3">
          <li><a href="/articles?category=Smartphones" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Smartphones</a></li>
          <li><a href="/articles?category=Laptops" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Laptops</a></li>
          <li><a href="/articles?category=Audio" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-block">Audio</a></li>
          <li><a href="/category" class="text-brand-blue hover:text-brand-blue/80 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Categories →</a></li>
        </ul>
      </div>
      
      <!-- Second Row - Second Column (Today's Deals) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Today's Deals
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <ul class="space-y-3">
          <li>
            <a href="/deals" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-flex items-center">
              <span class="bg-brand-blue text-white text-xs px-2 py-0.5 rounded-full mr-2 flex-shrink-0">40% OFF</span>
              <span>Wireless Headphones</span>
            </a>
          </li>
          <li>
            <a href="/deals" class="text-dark-on-surface/80 hover:text-brand-blue transition-all duration-300 hover:translate-x-1 inline-flex items-center">
              <span class="bg-brand-blue text-white text-xs px-2 py-0.5 rounded-full mr-2 flex-shrink-0">NEW</span>
              <span>Smart Home Bundles</span>
            </a>
          </li>
          <li><a href="/deals" class="text-brand-blue hover:text-brand-blue/80 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Deals →</a></li>
        </ul>
      </div>
      
      <!-- Second Row - Third Column (Latest Reviews) -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
          <span class="relative">
            Latest Reviews
            <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
          </span>
        </h3>
        <div class="space-y-4">
          <a href="/article/iphone-15-pro-max-review" class="group flex items-center gap-4">
            <div class="w-14 h-14 rounded-lg overflow-hidden flex-shrink-0 border-2 border-dark-border">
              <img src="/assets/images/deal-placeholder-1.jpg" alt="iPhone 15" class="w-full h-full object-cover group-hover:scale-110 transition-transform">
            </div>
            <div>
              <p class="text-dark-on-surface/80 group-hover:text-brand-blue transition-colors text-sm">iPhone 15 Pro Max Review</p>
              <p class="text-dark-on-surface/70 text-xs">October 12, 2023</p>
            </div>
          </a>
          <a href="/articles" class="text-xs text-brand-blue hover:text-brand-blue/80 inline-block mt-1">View All Articles →</a>
        </div>
      </div>
    </div>
    
    <!-- Tags Section -->
    <div class="mt-10 pt-8 border-t border-dark-border">
      <h3 class="text-xl font-ghibliHeading font-bold mb-4 text-dark-on-surface">
        <span class="relative">
          Popular Tags
          <span class="absolute -bottom-1 left-0 h-1 w-12 bg-brand-blue rounded-full"></span>
        </span>
      </h3>
      <div class="flex flex-wrap gap-2">
        <a href="/articles?tag=wireless" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Wireless</a>
        <a href="/articles?tag=budget" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Budget</a>
        <a href="/articles?tag=premium" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Premium</a>
        <a href="/articles?tag=gaming" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Gaming</a>
        <a href="/articles?tag=portable" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Portable</a>
        <a href="/articles?tag=bluetooth" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#Bluetooth</a>
        <a href="/articles?tag=usb-c" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#USB-C</a>
        <a href="/articles?tag=5g" class="text-xs bg-dark-surface/50 hover:bg-dark-surface/80 text-dark-on-surface hover:text-brand-blue px-3 py-1.5 rounded-full transition-colors">#5G</a>
        <a href="/tags" class="text-xs text-brand-blue hover:text-brand-blue/80 ml-1 flex items-center">View All Tags →</a>
      </div>
    </div>
    
    <!-- Copyright and Legal -->
    <div class="border-t border-dark-border mt-10 pt-8 flex flex-col md:flex-row justify-between items-center text-dark-on-surface/70 text-sm">
      <p>&copy; {currentYear} {siteConfig.name}. All rights reserved.</p>
      <div class="mt-4 md:mt-0 flex space-x-6">
        <a href="/privacy-policy" class="hover:text-brand-blue transition-colors">Privacy Policy</a>
        <a href="/terms-of-service" class="hover:text-brand-blue transition-colors">Terms of Service</a>
        <a href="/contact" class="hover:text-brand-blue transition-colors">Contact Us</a>
      </div>
    </div>
  </div>
</footer>