---
import { Image } from 'astro:assets';
import placeholder from '@/assets/images/deal-placeholder-2.jpg';
const { category } = Astro.props;
---
<section class="container mx-auto px-4">
  <h2 class="text-3xl md:text-4xl font-bold mb-8">
    Latest in <span class="text-brand-blue">{category.name}</span>
  </h2>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {category.articles.map(article => (
      <a href={`/article/${article.slug}`} class={`block bg-dark-surface rounded-2xl overflow-hidden group transition-transform duration-300 hover:-translate-y-2 hover:shadow-glow-lg animate-on-scroll ghibli-glow-enhanced`} style={`--glow-color: theme('colors.brand.blue');`}>
        <div class="relative h-48">
          <Image src={article.featured_image || placeholder} alt={article.title} width={400} height={200} class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
        </div>
        <div class="p-6">
          <h3 class="font-bold mb-2 line-clamp-2">{article.title}</h3>
          <p class="text-sm text-dark-on-surface/70 line-clamp-2">{article.excerpt}</p>
        </div>
      </a>
    ))}
  </div>
</section>
