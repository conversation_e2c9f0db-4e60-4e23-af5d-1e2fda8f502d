---
const { coupons } = Astro.props;
---
<section class="container mx-auto px-4">
  <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Exclusive Coupons</h2>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {coupons.map((coupon, index) => {
      const isLarge = index % 5 === 0 || index % 5 === 3; // Make some cards larger
      const colSpan = isLarge ? 'sm:col-span-2' : '';

      return (
        <a href={coupon.coupon_url} target="_blank" rel="noopener noreferrer" class={`block bg-dark-surface border-2 border-dashed border-dark-border rounded-2xl p-6 text-center group transition-all duration-300 hover:border-brand-blue hover:bg-dark-border/30 transform hover:-rotate-2 ${colSpan} animate-on-scroll ghibli-glow-enhanced`}>
          <h3 class="text-lg font-bold text-brand-blue">{coupon.discount_value}{coupon.discount_type === 'percentage' ? '%' : ''} OFF</h3>
          <p class="font-bold text-xl my-2">{coupon.title}</p>
          <div class="font-mono text-center bg-dark-bg text-brand-blue border border-brand-blue/50 rounded-lg py-2 px-4 inline-block mt-2 group-hover:bg-brand-blue group-hover:text-dark-bg transition-colors">
            {coupon.code}
          </div>
          <p class="text-xs text-dark-on-surface/50 mt-3">Expires: {new Date(coupon.end_date * 1000).toLocaleDateString()}</p>
        </a>
      );
    })}
  </div>
</section>
