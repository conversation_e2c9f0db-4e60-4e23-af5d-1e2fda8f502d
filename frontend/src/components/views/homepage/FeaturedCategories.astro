---
const { categories } = Astro.props;
---
<section class="container mx-auto px-4">
  <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Explore by Category</h2>
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
    {categories.map(category => (
      <a href={`/category/${category.slug}`} class="block bg-dark-surface p-8 rounded-2xl text-center group transition-all duration-300 hover:-translate-y-2 hover:shadow-glow-md ghibli-glow-enhanced" style={`--glow-color: theme('colors.brand.blue');`}>
        <h3 class="text-xl font-bold text-dark-on-surface group-hover:text-brand-blue transition-colors">{category.name}</h3>
        <p class="text-sm text-dark-on-surface/60 mt-1">{category.article_count || 0}+ Articles</p>
      </a>
    ))}
  </div>
</section>

