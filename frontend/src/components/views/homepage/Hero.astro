---
import { Image } from 'astro:assets';
import placeholder from '@/assets/images/deal-placeholder-1.jpg';
const { articles, backgroundArticle } = Astro.props;
---
<section class="relative overflow-hidden py-16 md:py-24 lg:py-32">
  <div class="absolute inset-0 z-0">
    <Image 
      src={backgroundArticle?.featured_image || placeholder} 
      alt="Hero Background" 
      width={1920} 
      height={1080} 
      class="w-full h-full object-cover opacity-50 transition-opacity duration-1000 ease-in-out"
      style="transform: translateZ(0);"
    />
    <div class="absolute inset-0 bg-gradient-to-b from-transparent to-dark-bg"></div>
  </div>

  <div class="container mx-auto px-4 relative z-10">
    <div class="grid md:grid-cols-2 gap-8 items-center">
      <div class="text-center md:text-left">
        <h1 class="text-4xl lg:text-6xl font-bold tracking-tighter mb-6 leading-tight animate-fade-in-up text-dark-on-surface">
          Insightful Reviews, <br />
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-brand-blue to-brand-blue">Smarter Choices.</span>
        </h1>
        <p class="max-w-xl mx-auto md:mx-0 text-lg lg:text-xl text-dark-on-surface mb-8 animate-fade-in-up" style="animation-delay: 0.3s;">
          Your ultimate guide to making informed purchasing decisions. We provide expert reviews, detailed analysis, and the latest deals on top brands.
        </p>
        <div class="flex justify-center md:justify-start gap-4 animate-fade-in-up" style="animation-delay: 0.6s;">
          <a href="/articles" class="bg-brand-blue text-white font-bold py-3 px-8 rounded-full hover:bg-brand-blue/90 transition-all duration-300 transform hover:scale-105 shadow-glow-md ghibli-glow-enhanced">
            Explore Reviews
          </a>
          <a href="/deals" class="bg-dark-surface text-dark-on-surface font-bold py-3 px-8 rounded-full hover:bg-dark-border transition-all duration-300 transform hover:scale-105 ghibli-glow-enhanced">
            Latest Deals
          </a>
        </div>
      </div>
      <div class="relative h-96 md:h-auto md:aspect-square perspective-1000">
        {articles?.map((article, index) => (
          <a href={`/article/${article.slug}`} class:list={[
            "absolute w-full h-full rounded-2xl overflow-hidden transition-all duration-700 ease-in-out transform-gpu shadow-lg hover:shadow-glow-lg",
            { 'z-10 opacity-100': index === 0, 'opacity-0': index !== 0 }
          ]} data-carousel-item={index}>
            <Image src={article.featured_image || placeholder} alt={article.title} width={600} height={600} class="w-full h-full object-cover" />
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
            <div class="absolute bottom-0 left-0 p-6 text-white">
              <h2 class="font-bold text-xl mb-2">{article.title}</h2>
              <p class="text-sm opacity-80">{article.excerpt}</p>
            </div>
          </a>
        ))}
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const items = document.querySelectorAll('[data-carousel-item]');
    let currentIndex = 0;

    function showItem(index) {
      items.forEach((item, i) => {
        const isCurrent = i === index;
        item.classList.toggle('z-10', isCurrent);
        item.classList.toggle('opacity-100', isCurrent);
        item.classList.toggle('opacity-0', !isCurrent);
      });
    }

    // Initial state
    showItem(currentIndex);

    setInterval(() => {
      currentIndex = (currentIndex + 1) % items.length;
      showItem(currentIndex);
    }, 5000);
  });
</script>
