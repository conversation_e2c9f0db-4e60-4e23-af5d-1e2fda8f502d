---
import { Image } from 'astro:assets';
import placeholder1 from '@/assets/images/deal-placeholder-1.jpg';
import placeholder2 from '@/assets/images/deal-placeholder-2.jpg';

const { deals } = Astro.props;

const leftColumnDeals = deals.slice(0, 6); // First 6 deals for the left column
const rightColumnDeals = deals.slice(6); // Remaining deals for the right column
---
<section class="container mx-auto px-4">
  <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Hot Deals</h2>
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <div class="lg:col-span-2 grid grid-cols-1 sm:grid-cols-3 gap-6">
      {leftColumnDeals.map((deal, index) => (
        <>
          {index % 2 === 0 ? (
            <a href={deal.deal_url} target="_blank" rel="noopener noreferrer" class="block rounded-2xl overflow-hidden group relative transform transition-transform duration-500 hover:-translate-y-2 ghibli-glow-enhanced h-full">
              <Image src={deal.image_url || placeholder1} alt={deal.title} width={600} height={500} class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
              <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
              <div class="absolute bottom-0 left-0 p-6 text-white">
                <h3 class="text-xl font-bold mb-2">{deal.title}</h3>
                <div class="flex items-baseline gap-3">
                  <span class="text-2xl font-bold text-brand-blue">${deal.sale_price}</span>
                  {deal.original_price && <span class="text-md line-through opacity-70">${deal.original_price}</span>}
                </div>
              </div>
            </a>
          ) : (
            <a href={deal.deal_url} target="_blank" rel="noopener noreferrer" class="block bg-dark-surface rounded-2xl p-4 group transition-all duration-300 hover:shadow-lg hover:border-brand-blue border border-transparent ghibli-glow-enhanced h-full">
              <div class="h-56 rounded-lg overflow-hidden mb-4">
                <Image src={deal.image_url || placeholder2} alt={deal.title} width={300} height={300} class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
              </div>
              <h3 class="font-bold line-clamp-2 mb-2">{deal.title}</h3>
              <div class="flex items-baseline gap-2">
                <span class="text-xl font-bold text-brand-blue">${deal.sale_price}</span>
                {deal.original_price && <span class="text-sm text-dark-on-surface/50 line-through">${deal.original_price}</span>}
              </div>
            </a>
          )}
        </>
      ))}
    </div>
    <div class="lg:col-span-1 flex flex-col gap-4">
      {rightColumnDeals.map(deal => (
        <a href={deal.deal_url} target="_blank" rel="noopener noreferrer" class="block bg-dark-surface rounded-2xl p-4 flex flex-col justify-between group transition-all duration-300 hover:shadow-lg hover:border-brand-blue border border-transparent ghibli-glow-enhanced">
          <div>
            <h3 class="font-bold text-md mb-1 line-clamp-2">{deal.title}</h3>
            <p class="text-dark-on-surface/70 text-xs line-clamp-3">{deal.description}</p>
          </div>
          <div class="flex items-baseline gap-2 mt-2">
            <span class="text-lg font-bold text-brand-blue">${deal.sale_price}</span>
            {deal.original_price && <span class="text-xs text-dark-on-surface/50 line-through">${deal.original_price}</span>}
          </div>
        </a>
      ))}
    </div>
  </div>
</section>