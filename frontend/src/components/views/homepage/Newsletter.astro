---
// No props needed for this component
---
<section class="container mx-auto px-4">
  <div class="bg-dark-surface rounded-2xl p-10 md:p-16 text-center max-w-4xl mx-auto border border-dark-border animate-on-scroll ghibli-glow-enhanced">
    <h2 class="text-3xl md:text-4xl font-bold mb-4">Stay Ahead of the Curve</h2>
    <p class="text-dark-on-surface/70 mb-8 max-w-2xl mx-auto">
      Subscribe to our newsletter for the latest reviews, deals, and tech insights delivered straight to your inbox.
    </p>
    <form class="flex flex-col sm:flex-row max-w-lg mx-auto gap-4">
      <input type="email" placeholder="Enter your email" class="flex-grow bg-dark-bg border border-dark-border rounded-full px-6 py-3 focus:outline-none focus:ring-2 focus:ring-brand-blue ghibli-glow-enhanced" />
      <button type="submit" class="bg-brand-blue text-white font-bold py-3 px-8 rounded-full hover:bg-brand-blue/90 transition-all duration-300 transform hover:scale-105 ghibli-glow-enhanced">
        Subscribe
      </button>
    </form>
  </div>
</section>
