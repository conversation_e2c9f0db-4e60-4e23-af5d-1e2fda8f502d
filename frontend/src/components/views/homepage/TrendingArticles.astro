---
import ArticleCard from '@/components/ArticleCard.astro';
import { Image } from 'astro:assets';

const { articles } = Astro.props;
---

<section class="container mx-auto px-4 py-12">
  <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Trending Articles</h2>
  <div class="relative group">
    <div id="trending-articles-carousel" class="flex overflow-x-hidden snap-x snap-mandatory scroll-smooth pb-4 no-scrollbar">
      {articles.map((article) => (
        <div class="flex-shrink-0 w-full md:w-1/2 lg:w-1/3 xl:w-1/4 snap-center px-2">
          <ArticleCard article={article} variant="default" size="large" />
        </div>
      ))}
    </div>

    <!-- Navigation Buttons -->
    <button id="trending-articles-prev" class="absolute left-0 top-1/2 -translate-y-1/2 bg-dark-surface/70 hover:bg-dark-surface rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 ghibli-glow-enhanced">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-dark-on-surface" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button id="trending-articles-next" class="absolute right-0 top-1/2 -translate-y-1/2 bg-dark-surface/70 hover:bg-dark-surface rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 ghibli-glow-enhanced">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-dark-on-surface" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const carousel = document.getElementById('trending-articles-carousel');
    const prevBtn = document.getElementById('trending-articles-prev');
    const nextBtn = document.getElementById('trending-articles-next');

    if (carousel && prevBtn && nextBtn) {
      const scrollAmount = carousel.querySelector('div').offsetWidth; // Width of one article card

      nextBtn.addEventListener('click', () => {
        carousel.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      });

      prevBtn.addEventListener('click', () => {
        carousel.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      });
    }
  });
</script>

<style>
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
</style>