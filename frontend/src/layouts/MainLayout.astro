---
import '../styles/global.css';
import '../styles/ghibli-effects-2025.css';
import { siteConfig } from '../config/site';
import Footer from '../components/Footer.astro';
import Header from '../components/Header.astro';
import Particles from '../components/Particles.astro';

interface Props {
  title?: string;
  description?: string;
  image?: string;
}

const { 
  title = siteConfig.name,
  description = siteConfig.description,
  image = '/images/default-og.png',
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title}</title>
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.site)} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.site)} />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    
    <script>
      // Set theme based on localStorage or system preference
      if (localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    <style>
      @keyframes gradient-animation {
        0% { background-position: 0% 50%; }
        25% { background-position: 50% 100%; }
        50% { background-position: 100% 50%; }
        75% { background-position: 50% 0%; }
        100% { background-position: 0% 50%; }
      }
      body {
        background: linear-gradient(-45deg, #1a202c, #2d3748, #3a475a, #2d3748, #1a202c);
        background-size: 400% 400%;
        animation: gradient-animation 20s ease infinite;
      }
      .mouse-trail {
        position: fixed;
        pointer-events: none;
        width: 20px;
        height: 20px;
        background: radial-gradient(circle, rgba(139, 92, 246, 0.8) 0%, rgba(139, 92, 246, 0) 70%); /* brand-purple with transparency */
        border-radius: 50%;
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.2s ease-out, opacity 0.5s ease-out;
        z-index: 9999;
        box-shadow: 0 0 15px rgba(139, 92, 246, 0.6);
      }
      /* Scroll Animation Styles */
      .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
      }
      .animate-on-scroll.is-visible {
        opacity: 1;
        transform: translateY(0);
      }
    </style>
  </head>
  <body class="bg-dark-bg text-dark-on-surface font-sans flex flex-col min-h-screen ghibli-glow-enhanced ghibli-mouse-light">
    <Particles />
    <div class="relative z-10 flex flex-col flex-grow">
      <Header />
      <main class="flex-grow ghibli-crystal-atmosphere">
        <slot />
      </main>
      <Footer />
    </div>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        // Mouse Light Following Effect
        const body = document.body;
        body.addEventListener('mousemove', (e) => {
          body.style.setProperty('--mouse-x', `${e.clientX}px`);
          body.style.setProperty('--mouse-y', `${e.clientY}px`);
        });

        // Scroll Animation
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('is-visible');
            } else {
              // Optional: remove class when out of view if you want to re-animate on scroll back
              // entry.target.classList.remove('is-visible');
            }
          });
        }, {
          threshold: 0.1 // Trigger when 10% of the element is visible
        });

        document.querySelectorAll('.animate-on-scroll').forEach(element => {
          observer.observe(element);
        });
      });
    </script>
  </body>
</html>
