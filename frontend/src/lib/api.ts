// API Service for managing all backend API calls
import { getEnvConfig } from './env';
import * as fs from 'fs';
import * as path from 'path';

// Types for API responses and requests
export interface TagDetailResp {
  id: number;
  slug: string;
  name: string;
  featured: boolean;
}

export interface CategoryDetailResp {
  id: number;
  slug: string;
  name: string;
  icon: string;
  description: string;
  featured: boolean;
}

export interface ArticleDetailResp {
  id: number;
  slug: string;
  title: string;
  description: string;
  content: string;
  cover_image?: string;
  featured_image?: string;
  publish_date: string;
  published_at?: Date;
  reading_time?: number;
  category_info?: {
    id: number;
    slug: string;
    name: string;
  };
  author?: {
    id: number;
    name: string;
    avatar?: string;
  };
  brand_info?: BrandDetailResp;
  deal_list?: DealDetailResp[];
  tag_list?: TagDetailResp[];
  coupon_list?: CouponDetailResp[];
  created_at: string;
  updated_at: string;
  excerpt?: string;
}

export interface BrandDetailResp {
  id: number;
  slug: string;
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  affiliate_link?: string;
  featured?: boolean;
  active?: boolean;
  category_id?: number;
  category_slug?: string;
}

export interface DealDetailResp {
  id: number;
  slug: string;
  title: string;
  description?: string;
  deal_url?: string;
  image_url?: string;
  original_price?: number;
  sale_price?: number;
  discount_type?: string;
  discount_value?: number;
  featured?: boolean;
  active?: boolean;
  verified?: boolean;
  start_date?: number;
  end_date?: number;
  brand_id?: number;
  brand_slug?: string;
  category_id?: number;
  category_slug?: string;
  store?: string;
}

// 后端API响应格式
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface TagListData {
  total: number;
  page: number;
  page_size: number;
  tag_list: TagDetailResp[];
}

export interface TagListResp extends ApiResponse<TagListData> {}

export interface GetTagListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  search?: string;
}

export interface CategoryListData {
  total: number;
  page: number;
  page_size: number;
  category_list: CategoryDetailResp[];
}

export interface CategoryListResp extends ApiResponse<CategoryListData> {}

export interface GetCategoryListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  search?: string;
}

export interface ArticleListData {
  total: number;
  page: number;
  page_size: number;
  article_list: ArticleDetailResp[];
}

export interface ArticleListResp extends ApiResponse<ArticleListData> {}

export interface GetArticleListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  search?: string;
  sort_by?: string;
  sort_direction?: string;
  category?: string;
  brand?: string;
}

export interface BrandListData {
  total: number;
  page: number;
  page_size: number;
  brand_list: BrandDetailResp[];
}

export interface BrandListResp extends ApiResponse<BrandListData> {}

export interface GetBrandListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  search?: string;
  active?: boolean;
  category_id?: number;
}

export interface DealListData {
  total: number;
  page: number;
  page_size: number;
  deal_list: DealDetailResp[];
}

export interface DealListResp extends ApiResponse<DealListData> {}

export interface GetDealListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  search?: string;
  active?: boolean;
  verified?: boolean;
  valid_only?: boolean;
  brand_id?: number;
  category_id?: number;
  sort_by?: string;
  sort_direction?: string;
}

// API configuration
const API_TIMEOUT = 10000; // 10 seconds timeout for API calls
const API_VERSION = 'v1';

/**
 * 读取简单的环境配置文件
 */
const getEnvironment = (): string => {
  try {
    // 尝试读取项目根目录的env文件
    const envPath = path.resolve(process.cwd(), 'env');
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8').trim();
      const match = envContent.match(/env=(\w+)/);
      if (match && match[1]) {
        return match[1]; // 返回env=后面的值
      }
    }
  } catch (error) {
    console.warn('无法读取env文件，使用默认环境', error);
  }
  
  return 'local'; // 默认返回local环境
};

/**
 * Get base API URL based on environment configuration
 */
export const getApiBaseUrl = (): string => {
  const env = getEnvironment();
  
  if (env === 'local') {
    return 'http://127.0.0.1:9091';
  } else if (env === 'live') {
    return 'https://smartreviews.top';
  }
  
  // 默认返回本地开发URL
  return 'http://127.0.0.1:9091';
};

/**
 * Helper function for handling API errors
 */
const handleApiError = (error: any): never => {
  console.error('API Error:', error);
  
  // Format error message based on error type
  let errorMessage = 'Unknown error occurred';
  
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  } else if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  }
  
  throw new Error(`API request failed: ${errorMessage}`);
};

/**
 * Create a fetch request with timeout
 */
async function fetchWithTimeout(url: string, options: RequestInit = {}, timeout = API_TIMEOUT): Promise<Response> {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  
  try {
    console.log(`Sending request to: ${url}`);
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    if (error.name === 'AbortError') {
      throw new Error(`Request timed out after ${timeout}ms`);
    }
    throw error;
  }
}

// Category API methods
export const CategoryApi = {
  /**
   * Get list of categories from the backend
   */
  async getCategoryList(params: GetCategoryListReq = {}): Promise<CategoryListData> {
    try {
      const queryParams = new URLSearchParams();
      
      // Ensure page parameter is correctly passed
      const page = params.page || 1;
      const pageSize = params.page_size || 30;
      
      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());
      
      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.search) queryParams.append('search', params.search);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/category${queryString}`;
      
      console.log(`Fetching categories from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse: ApiResponse<CategoryListData> = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  
  /**
   * Get a specific category by slug
   * Note: This API method is not implemented yet on the backend
   */
  async getCategoryBySlug(slug: string): Promise<CategoryDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/category/${slug}`;
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch category details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Tag API methods
export const TagApi = {
  /**
   * Get list of tags from the backend
   */
  async getTagList(params: GetTagListReq = {}): Promise<TagListData> {
    try {
      const queryParams = new URLSearchParams();
      
      // Ensure page parameter is correctly passed
      const page = params.page || 1;
      const pageSize = params.page_size || 50;
      
      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());
      
      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.search) queryParams.append('search', params.search);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/tag${queryString}`;
      
      console.log(`Fetching tags from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch tags: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse: ApiResponse<TagListData> = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  
  /**
   * Get a specific tag by slug
   * Note: This API method is not implemented yet on the backend
   */
  async getTagBySlug(slug: string): Promise<TagDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/tag/${slug}`;
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch tag details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Article API methods
export const ArticleApi = {
  /**
   * Get list of articles from the backend
   */
  async getArticleList(params: GetArticleListReq = {}): Promise<ArticleListData> {
    try {
      const queryParams = new URLSearchParams();
      
      // Ensure page parameter is correctly passed
      const page = params.page || 1;
      const pageSize = params.page_size || 30;
      
      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());
      
      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.category) queryParams.append('category', params.category);
      if (params.brand) queryParams.append('brand', params.brand);
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/article${queryString}`;
      
      console.log(`Fetching articles from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch articles: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse: ApiResponse<ArticleListData> = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  
  /**
   * Get a specific article by slug
   */
  async getArticleBySlug(slug: string): Promise<ArticleDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/article/slug/${slug}`;
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch article details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Brand API methods
export const BrandApi = {
  /**
   * Get list of brands from the backend
   */
  async getBrandList(params: GetBrandListReq = {}): Promise<BrandListData> {
    try {
      const queryParams = new URLSearchParams();
      
      // Ensure page parameter is correctly passed
      const page = params.page || 1;
      const pageSize = params.page_size || 30;
      
      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());
      
      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.category_id) queryParams.append('category_id', params.category_id.toString());
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/brand${queryString}`;
      
      console.log(`Fetching brands from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch brands: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse: ApiResponse<BrandListData> = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  
  /**
   * Get a specific brand by slug
   */
  async getBrandBySlug(slug: string): Promise<BrandDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/brand/slug/${slug}`;
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch brand details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Deal API methods
export const DealApi = {
  /**
   * Get list of deals from the backend
   */
  async getDealList(params: GetDealListReq = {}): Promise<DealListData> {
    try {
      const queryParams = new URLSearchParams();
      
      // Ensure page parameter is correctly passed
      const page = params.page || 1;
      const pageSize = params.page_size || 30;
      
      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());
      
      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.verified !== undefined) queryParams.append('verified', params.verified.toString());
      if (params.valid_only !== undefined) queryParams.append('valid_only', params.valid_only.toString());
      if (params.brand_id) queryParams.append('brand_id', params.brand_id.toString());
      if (params.category_id) queryParams.append('category_id', params.category_id.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/deal${queryString}`;
      
      console.log(`Fetching deals from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch deals: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse: ApiResponse<DealListData> = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  
  /**
   * Get a specific deal by slug
   */
  async getDealBySlug(slug: string): Promise<DealDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/deal/slug/${slug}`;
      const response = await fetchWithTimeout(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch deal details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const apiResponse = await response.json();
      
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Coupon API interfaces
export interface GetCouponListReq {
  page?: number;
  page_size?: number;
  featured?: boolean;
  active?: boolean;
  verified?: boolean;
  valid_only?: boolean;
  available_only?: boolean;
  brand_id?: number;
  category_id?: number;
  discount_type?: string;
  search?: string;
}

export interface CouponDetailResp {
  id: number;
  slug: string;
  title: string;
  description: string;
  code: string;
  coupon_url: string;
  discount_type: string;
  discount_value: number;
  min_order_value: number;
  max_discount: number;
  featured: boolean;
  active: boolean;
  verified: boolean;
  start_date: number;
  end_date: number;
  usage_limit: number;
  used_count: number;
  user_usage_limit: number;
  brand_id: number;
  brand_slug: string;
  category_id: number;
  category_slug: string;
}

export interface CouponListData {
  total: number;
  page: number;
  page_size: number;
  coupon_list: CouponDetailResp[];
}

// Coupon API methods
export const CouponApi = {
  /**
   * Get list of coupons from the backend
   */
  async getCouponList(params: GetCouponListReq = {}): Promise<CouponListData> {
    try {
      const queryParams = new URLSearchParams();

      const page = params.page || 1;
      const pageSize = params.page_size || 30;

      queryParams.append('page', page.toString());
      queryParams.append('page_size', pageSize.toString());

      if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.verified !== undefined) queryParams.append('verified', params.verified.toString());
      if (params.valid_only !== undefined) queryParams.append('valid_only', params.valid_only.toString());
      if (params.available_only !== undefined) queryParams.append('available_only', params.available_only.toString());
      if (params.brand_id) queryParams.append('brand_id', params.brand_id.toString());
      if (params.category_id) queryParams.append('category_id', params.category_id.toString());
      if (params.discount_type) queryParams.append('discount_type', params.discount_type);
      if (params.search) queryParams.append('search', params.search);

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/coupon${queryString}`;

      const response = await fetchWithTimeout(url);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch coupons: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const apiResponse = await response.json();

      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }

      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  /**
   * Get a specific coupon by ID
   */
  async getCouponById(id: number): Promise<CouponDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/coupon/${id}`;
      const response = await fetchWithTimeout(url);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch coupon details: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const apiResponse = await response.json();

      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }

      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  /**
   * Get a specific coupon by slug
   */
  async getCouponBySlug(slug: string): Promise<CouponDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/coupon/slug/${slug}`;
      const response = await fetchWithTimeout(url);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch coupon details: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const apiResponse = await response.json();

      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }

      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  /**
   * Get a specific coupon by code
   */
  async getCouponByCode(code: string): Promise<CouponDetailResp> {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/coupon/code/${code}`;
      const response = await fetchWithTimeout(url);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch coupon details: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const apiResponse = await response.json();

      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }

      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};

// Default API object with all API methods
const Api = {
  Tag: TagApi,
  Category: CategoryApi,
  Article: ArticleApi,
  Brand: BrandApi,
  Deal: DealApi,
  Coupon: CouponApi
};

export default Api;