---
import MainLayout from '@/layouts/MainLayout.astro';
import Api from '@/lib/api';
import { Image } from 'astro:assets';
import placeholder from '@/assets/images/deal-placeholder-3.jpg';
import { marked } from 'marked';

// Get slug from URL params (SSR mode)
const { slug } = Astro.params;

let articleData;
let relatedArticlesData = { article_list: [] };
let relatedDealsData = { deal_list: [] };
let relatedBrandsData = { brand_list: [] };

if (slug) {
  try {
    articleData = await Api.Article.getArticleBySlug(slug);

    // Fetch related content based on article's category or tags
    const [articlesResp, dealsResp, brandsResp] = await Promise.all([
      articleData.category_info?.slug ? Api.Article.getArticleList({ page_size: 3, category: articleData.category_info.slug, exclude_slug: slug }) : Promise.resolve({ article_list: [] }),
      articleData.brand_info?.id ? Api.Deal.getDealList({ page_size: 3, brand_id: articleData.brand_info.id }) : Promise.resolve({ deal_list: [] }),
      articleData.brand_info?.id ? Api.Brand.getBrandList({ page_size: 3, category_id: articleData.brand_info.category_id }) : Promise.resolve({ brand_list: [] }),
    ]);

    relatedArticlesData = articlesResp;
    relatedDealsData = dealsResp;
    relatedBrandsData = brandsResp;

  } catch (error) {
    console.error(`Failed to fetch article or related content for slug ${slug}:`, error);
    console.error("Error details:", JSON.stringify(error, Object.getOwnPropertyNames(error)));
    // Handle 404 or other errors gracefully
    // You might redirect to a 404 page or display an error message
    // For now, we'll just set articleData to null to prevent rendering
    articleData = null;
  }
}

// Helper to format date
const formatDate = (dateString) => new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

// If articleData is null, render a 404 page or an error message
if (!articleData) {
  return Astro.redirect('/404'); // Redirect to a 404 page
}

const article = articleData;
const relatedArticles = relatedArticlesData?.article_list || [];
const relatedDeals = relatedDealsData?.deal_list || [];
const relatedBrands = relatedBrandsData?.brand_list || [];
---

<MainLayout
  title={`${article.title} | BrandReviews`}
  description={article.excerpt || article.description}
  image={article.featured_image || placeholder}
>
  <article class="container mx-auto px-4 py-16">
    <!-- Article Header with Featured Image -->
    <header class="relative mb-12 rounded-2xl overflow-hidden shadow-xl ghibli-glow-enhanced">
      <Image 
        src={article.featured_image || placeholder}
        alt={article.title}
        width={1200}
        height={600}
        class="w-full h-96 object-cover"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
      <div class="absolute bottom-0 left-0 p-8 text-white w-full">
        <span class="inline-block bg-brand-blue/80 text-white text-sm font-bold px-4 py-1 rounded-full mb-3 backdrop-blur-sm ghibli-glow-enhanced">
          {article.category_info?.name || 'Article'}
        </span>
        <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-4">{article.title}</h1>
        <div class="flex items-center text-sm text-white/80">
          <span>By {article.author?.name || 'Editorial Team'}</span>
          <span class="mx-3">•</span>
          <span>{formatDate(article.publish_date)}</span>
          {article.reading_time && (
            <>
              <span class="mx-3">•</span>
              <span>{article.reading_time} min read</span>
            </>
          )}
        </div>
      </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
      <!-- Article Content -->
      <div class="lg:col-span-2">
        <div class="prose prose-lg dark:prose-invert max-w-none text-dark-on-surface">
          <Fragment set:html={marked.parse(article.content)} />
        </div>

        <!-- Article Tags -->
        {article.tag_list && article.tag_list.length > 0 && (
          <div class="mt-12 border-t border-dark-border pt-8">
            <h3 class="text-xl font-bold mb-4">Tags:</h3>
            <div class="flex flex-wrap gap-2">
              {article.tag_list.map(tag => (
                <a href={`/tag/${tag.slug}`} class="bg-dark-surface border border-dark-border rounded-full px-4 py-2 text-sm hover:bg-dark-border transition-colors ghibli-glow-enhanced">
                  #{tag.name}
                </a>
              ))}
            </div>
          </div>
        )}

        <!-- Author Box -->
        {article.author && (
          <div class="mt-12 p-6 bg-dark-surface rounded-2xl border border-dark-border flex items-center gap-4 ghibli-glow-enhanced">
            <Image 
              src={article.author.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author.name)}&background=random&color=fff`}
              alt={article.author.name}
              width={80}
              height={80}
              class="rounded-full object-cover"
            />
            <div>
              <h3 class="text-xl font-bold mb-1">About {article.author.name}</h3>
              <p class="text-dark-on-surface/70">Our expert writer specializing in {article.category_info?.name || 'technology'} reviews.</p>
            </div>
          </div>
        )}
      </div>

      <!-- Sidebar -->
      <aside class="lg:col-span-1 space-y-12">
        <!-- Related Articles -->
        {relatedArticles.length > 0 && (
          <div class="bg-dark-surface rounded-2xl p-6 border border-dark-border ghibli-glow-enhanced">
            <h3 class="text-xl font-bold mb-6">Related Articles</h3>
            <div class="space-y-4">
              {relatedArticles.map(relArticle => (
                <a href={`/article/${relArticle.slug}`} class="block group">
                  <div class="flex items-center gap-4">
                    <Image 
                      src={relArticle.featured_image || placeholder}
                      alt={relArticle.title}
                      width={80}
                      height={80}
                      class="rounded-lg object-cover flex-shrink-0"
                    />
                    <div>
                      <h4 class="font-bold line-clamp-2 group-hover:text-brand-blue transition-colors">{relArticle.title}</h4>
                      <p class="text-sm text-dark-on-surface/70">{formatDate(relArticle.publish_date)}</p>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}

        <!-- Related Deals -->
        {relatedDeals.length > 0 && (
          <div class="bg-dark-surface rounded-2xl p-6 border border-dark-border ghibli-glow-enhanced">
            <h3 class="text-xl font-bold mb-6">Related Deals</h3>
            <div class="space-y-4">
              {relatedDeals.map(deal => (
                <a href={deal.deal_url} target="_blank" rel="noopener noreferrer" class="block group">
                  <div class="flex items-center gap-4">
                    <Image 
                      src={deal.image_url || placeholder}
                      alt={deal.title}
                      width={80}
                      height={80}
                      class="rounded-lg object-cover flex-shrink-0"
                    />
                    <div>
                      <h4 class="font-bold line-clamp-2 group-hover:text-brand-blue transition-colors">{deal.title}</h4>
                      <p class="text-sm text-dark-on-surface/70">${deal.sale_price} <span class="line-through">${deal.original_price}</span></p>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}

        <!-- Related Brands -->
        {relatedBrands.length > 0 && (
          <div class="bg-dark-surface rounded-2xl p-6 border border-dark-border ghibli-glow-enhanced">
            <h3 class="text-xl font-bold mb-6">Related Brands</h3>
            <div class="space-y-4">
              {relatedBrands.map(brand => (
                <a href={`/brand/${brand.slug}`} class="block group">
                  <div class="flex items-center gap-4">
                    <Image 
                      src={brand.logo || placeholder}
                      alt={brand.name}
                      width={80}
                      height={80}
                      class="rounded-lg object-contain flex-shrink-0"
                    />
                    <div>
                      <h4 class="font-bold line-clamp-2 group-hover:text-brand-blue transition-colors">{brand.name}</h4>
                      <p class="text-sm text-dark-on-surface/70">{brand.description?.substring(0, 50)}...</p>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}
      </aside>
    </div>
  </article>
</MainLayout>
