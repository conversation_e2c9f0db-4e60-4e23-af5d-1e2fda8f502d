---
import MainLayout from '@/layouts/MainLayout.astro';
import Api, { type ArticleDetailResp } from '@/lib/api';
import { Image } from 'astro:assets';
import placeholder from '@/assets/images/deal-placeholder-1.jpg';

// --- Data Fetching ---
const url = Astro.url;
const currentPage = parseInt(url.searchParams.get('page') || '1');
const searchQuery = url.searchParams.get('search') || '';
const category = url.searchParams.get('category') || '';
const tag = url.searchParams.get('tag') || '';
const articlesPerPage = 9;

const sortBy = url.searchParams.get('sort_by') || 'publish_date';
const sortDirection = url.searchParams.get('sort_direction') || 'desc';

const [
  articleData,
  categoriesData,
  tagsData,
] = await Promise.all([
  Api.Article.getArticleList({
    page: currentPage,
    page_size: articlesPerPage,
    search: searchQuery,
    category: category,
    tag: tag,
    sort_by: sortBy,
    sort_direction: sortDirection,
  }),

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <select name="category" onchange="this.form.submit()" class="w-full bg-dark-surface border border-dark-border rounded-full px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-brand-blue">
            <option value="">All Categories</option>
            {categories.map(category => (
              <option value={category.slug} selected={category.slug === category}>{category.name}</option>
            ))}
          </select>
          <select name="tag" onchange="this.form.submit()" class="w-full bg-dark-surface border border-dark-border rounded-full px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-brand-blue">
            <option value="">All Tags</option>
            {tags.map(tag => (
              <option value={tag.slug} selected={tag.slug === tag}>{tag.name}</option>
            ))}
          </select>
          <select name="sort_by" onchange="this.form.submit()" class="w-full bg-dark-surface border border-dark-border rounded-full px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-brand-blue">
            <option value="publish_date" selected={sortBy === 'publish_date'}>Publish Date</option>
            <option value="title" selected={sortBy === 'title'}>Title</option>
            <option value="reading_time" selected={sortBy === 'reading_time'}>Reading Time</option>
          </select>
          <select name="sort_direction" onchange="this.form.submit()" class="w-full bg-dark-surface border border-dark-border rounded-full px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-brand-blue">
            <option value="desc" selected={sortDirection === 'desc'}>Descending</option>
            <option value="asc" selected={sortDirection === 'asc'}>Ascending</option>
          </select>
        </div>
      </form>
    </div>

    <!-- Articles Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {articles.map(article => (
        <a href={`/article/${article.slug}`} class="block bg-dark-surface rounded-2xl overflow-hidden group transition-transform duration-300 hover:-translate-y-2 hover:shadow-glow-lg" style={`--glow-color: theme('colors.brand.blue');`}>
          <div class="relative h-48">
            <Image
              src={article.featured_image || placeholder}
              alt={article.title}
              width={400}
              height={224}
              class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
            <span class="absolute top-4 left-4 bg-brand-blue/80 text-white text-xs font-bold px-3 py-1 rounded-full backdrop-blur-sm">{article.category?.name || 'Review'}</span>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-3 line-clamp-2 min-h-[3rem] text-dark-on-surface">{article.title}</h3>
            <p class="text-dark-on-surface/80 line-clamp-3 min-h-[4.5rem] mb-4">{article.excerpt || article.description}</p>
            <div class="flex items-center text-sm text-dark-on-surface/70">
              <span>{formatDate(article.publish_date)}</span>
              <span class="mx-2">•</span>
              <span>{article.reading_time || 5} min read</span>
            </div>
          </div>
        </a>
      ))}
    </div>

    <!-- Pagination -->
    {totalPages > 1 && (
      <nav class="mt-16 flex justify-center items-center gap-4">
        {currentPage > 1 && (
          <a href={getPaginationUrl(currentPage - 1)} class="bg-dark-surface border border-dark-border rounded-full px-4 py-2 hover:bg-dark-border transition-colors">
            &larr; Previous
          </a>
        )}
        <span class="text-dark-on-surface/70">
          Page {currentPage} of {totalPages}
        </span>
        {currentPage < totalPages && (
          <a href={getPaginationUrl(currentPage + 1)} class="bg-dark-surface border border-dark-border rounded-full px-4 py-2 hover:bg-dark-border transition-colors">
            Next &rarr;
          </a>
        )}
      </nav>
    )}

  </div>
</MainLayout>