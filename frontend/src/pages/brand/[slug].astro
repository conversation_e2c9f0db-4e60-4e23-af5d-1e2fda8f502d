---
import MainLayout from '@/layouts/MainLayout.astro';
import Api, { type BrandDetailResp, type ArticleDetailResp, type DealDetailResp, type CouponDetailResp } from '@/lib/api';
import { Image } from 'astro:assets';
import brandPlaceholder from '@/assets/images/brand-placeholder.svg';
import articlePlaceholder from '@/assets/images/deal-placeholder-1.jpg';
import dealPlaceholder from '@/assets/images/deal-placeholder-2.jpg';

// Get slug from URL params (SSR mode)
const { slug } = Astro.params;

let brand: BrandDetailResp | null = null;
let articles: ArticleDetailResp[] = [];
let deals: DealDetailResp[] = [];
let coupons: CouponDetailResp[] = [];

if (slug) {
  try {
    brand = await Api.Brand.getBrandBySlug(slug);

    const [articlesData, dealsData, couponsData] = await Promise.all([
      Api.Article.getArticleList({ brand: brand.slug, page_size: 6 }).catch(() => ({ article_list: [] })),
      Api.Deal.getDealList({ brand_id: brand.id, page_size: 6 }).catch(() => ({ deal_list: [] })),
      Api.Coupon.getCouponList({ brand_id: brand.id, page_size: 6 }).catch(() => ({ coupon_list: [] }))
    ]);

    articles = articlesData.article_list || [];
    deals = dealsData.deal_list || [];
    coupons = couponsData.coupon_list || [];

  } catch (error) {
    console.error(`Failed to fetch brand details for slug ${slug}:`, error);
    brand = null;
  }
}

const formatPrice = (price: number | undefined): string => {
  if (price === undefined || isNaN(price)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(price);
};

const calculateDiscount = (original: number | undefined, sale: number | undefined): string => {
  if (!original || !sale || original <= 0) return '';
  const percentage = Math.round(((original - sale) / original) * 100);
  return percentage > 0 ? `${percentage}% OFF` : '';
};

const formatExpiryDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Limited time';

  const expiryDate = new Date(timestamp * 1000);
  const today = new Date();
  const diffTime: number = expiryDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) return 'Expired';
  if (diffDays === 0) return 'Expires today';
  if (diffDays === 1) return 'Expires tomorrow';
  return `${diffDays} days left`;
};
---

{brand ? (
<MainLayout
  title={`${brand.name} Reviews & Deals | BrandReviews`}
  description={brand.description || `Discover the latest ${brand.name} products, reviews, deals, and exclusive coupons. Expert insights and buying guides.`}
>
  <!-- Brand Hero Section -->
  <section class="relative py-16 md:py-24 bg-dark-surface border-b border-dark-border">
    <div class="container mx-auto px-4 text-center">
      <div class="mb-8">
        {brand.logo && (
          <div class="w-32 h-32 mx-auto mb-6 bg-dark-bg rounded-full p-4 flex items-center justify-center shadow-lg">
            <Image
              src={brand.logo && (brand.logo.startsWith('http://') || brand.logo.startsWith('https://')) ? brand.logo : brandPlaceholder}
              alt={`${brand.name} logo`}
              width={96}
              height={96}
              class="w-full h-full object-contain"
            />
          </div>
        )}
        <h1 class="text-4xl md:text-5xl font-bold mb-4">{brand.name}</h1>
        {brand.description && (
          <p class="text-lg text-dark-on-surface/70 max-w-3xl mx-auto">{brand.description}</p>
        )}
        {brand.website && (
          <a
            href={brand.website}
            target="_blank"
            rel="noopener noreferrer"
            class="inline-flex items-center mt-6 bg-brand-blue text-white font-bold py-2 px-6 rounded-full hover:bg-brand-blue/90 transition-colors transform hover:scale-105"
          >
            Visit Official Website
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" /></svg>
          </a>
        )}
      </div>
    </div>
  </section>

  <!-- Quick Navigation -->
  <section class="py-4 bg-dark-surface sticky top-16 z-40 border-b border-dark-border">
    <div class="container mx-auto px-4">
      <nav class="flex justify-center space-x-6">
        <a href="#reviews" class="text-dark-on-surface/70 hover:text-brand-blue font-medium transition-colors">Reviews</a>
        <a href="#deals" class="text-dark-on-surface/70 hover:text-brand-blue font-medium transition-colors">Deals</a>
        <a href="#coupons" class="text-dark-on-surface/70 hover:text-brand-blue font-medium transition-colors">Coupons</a>
      </nav>
    </div>
  </section>

  <!-- Latest Reviews Section -->
  <section id="reviews" class="py-16">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Latest Reviews for {brand.name}</h2>
      {articles.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {articles.map(article => (
            <a href={`/article/${article.slug}`} class="block bg-dark-surface rounded-2xl overflow-hidden group transition-transform duration-300 hover:-translate-y-2 hover:shadow-glow-lg" style="--glow-color: #3B82F6;">
              <div class="relative h-56">
                <Image 
                  src={article.cover_image || articlePlaceholder}
                  alt={article.title}
                  width={400}
                  height={224}
                  class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <span class="absolute top-4 left-4 bg-brand-blue/80 text-white text-xs font-bold px-3 py-1 rounded-full backdrop-blur-sm">{article.category?.name || 'Review'}</span>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-bold mb-3 line-clamp-2">{article.title}</h3>
                <p class="text-dark-on-surface/70 line-clamp-3 mb-4">{article.excerpt || article.description}</p>
                <div class="flex items-center text-sm text-dark-on-surface/60">
                  <span>{new Date(article.publish_date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                  <span class="mx-2">•</span>
                  <span>{article.reading_time || 5} min read</span>
                </div>
              </div>
            </a>
          ))}
        </div>
      ) : (
        <div class="text-center text-dark-on-surface/70">
          <p>No reviews available for {brand.name} yet. Check back soon!</p>
        </div>
      )}
    </div>
  </section>

  <!-- Hot Deals Section -->
  <section id="deals" class="py-16 bg-dark-bg">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Hot Deals for {brand.name}</h2>
      {deals.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {deals.map(deal => (
            <a href={deal.deal_url} target="_blank" rel="noopener noreferrer" class="block bg-dark-surface border border-dark-border rounded-2xl p-4 group transition-all duration-300 hover:border-brand-blue hover:shadow-glow-md hover:-translate-y-2" style="--glow-color: #3B82F6;">
              <div class="h-48 rounded-lg overflow-hidden mb-4">
                <Image 
                  src={deal.image_url || dealPlaceholder} 
                  alt={deal.title}
                  width={300}
                  height={192}
                  class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
              </div>
              <h3 class="font-bold line-clamp-2 mb-2">{deal.title}</h3>
              <div class="flex items-baseline gap-2">
                <span class="text-2xl font-bold text-brand-blue">{formatPrice(deal.sale_price)}</span>
                {deal.original_price && (
                  <span class="text-sm text-dark-on-surface/50 line-through">{formatPrice(deal.original_price)}</span>
                )}
              </div>
            </a>
          ))}
        </div>
      ) : (
        <div class="text-center text-dark-on-surface/70">
          <p>No deals available for {brand.name} yet. Check back soon!</p>
        </div>
      )}
    </div>
  </section>

  <!-- Exclusive Coupons Section -->
  <section id="coupons" class="py-16">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Exclusive Coupons for {brand.name}</h2>
      {coupons.length > 0 ? (
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {coupons.map(coupon => (
            <a href={coupon.coupon_url} target="_blank" rel="noopener noreferrer" class="block bg-dark-surface border-2 border-dashed border-dark-border rounded-2xl p-6 text-center group transition-all duration-300 hover:border-brand-purple hover:bg-dark-border/30 transform hover:-rotate-2">
              <h3 class="text-lg font-bold text-brand-purple">{coupon.discount_value}{coupon.discount_type === 'percentage' ? '%' : '$'} OFF</h3>
              <p class="font-bold text-xl my-2">{coupon.title}</p>
              <div class="font-mono text-center bg-dark-bg text-brand-purple border border-brand-purple/50 rounded-lg py-2 px-4 inline-block mt-2 group-hover:bg-brand-purple group-hover:text-dark-bg transition-colors">
                {coupon.code}
              </div>
              <p class="text-xs text-dark-on-surface/50 mt-3">Expires: {formatExpiryDate(coupon.end_date)}</p>
            </a>
          ))}
        </div>
      ) : (
        <div class="text-center text-dark-on-surface/70">
          <p>No coupons available for {brand.name} yet. Check back soon!</p>
        </div>
      )}
    </div>
  </section>
</MainLayout>
) : (
<MainLayout title="Brand Not Found | BrandReviews">
  <div class="container mx-auto px-4 py-16 text-center">
    <h1 class="text-4xl font-bold mb-4">Brand Not Found</h1>
    <p class="text-dark-on-surface/70 mb-8">The brand you're looking for doesn't exist or has been removed.</p>
    <a href="/brands" class="bg-brand-blue text-white font-bold py-3 px-6 rounded-full hover:bg-brand-blue/90 transition-colors">
      Browse All Brands
    </a>
  </div>
</MainLayout>
)}
