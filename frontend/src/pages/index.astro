---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';
import Api from '../lib/api';
import { Image } from 'astro:assets';
import Hero from '@/components/views/homepage/Hero.astro';
import FeaturedBrands from '@/components/views/homepage/FeaturedBrands.astro';
import HotDeals from '@/components/views/homepage/HotDeals.astro';
import FeaturedCategories from '@/components/views/homepage/FeaturedCategories.astro';
import FeaturedArticles from '@/components/views/homepage/FeaturedArticles.astro';
import ExclusiveCoupons from '@/components/views/homepage/ExclusiveCoupons.astro';
import PopularTags from '@/components/views/homepage/PopularTags.astro';
import CategoryArticles from '@/components/views/homepage/CategoryArticles.astro';
import LatestArticles from '@/components/views/homepage/LatestArticles.astro';
import TrendingArticles from '@/components/views/homepage/TrendingArticles.astro';
import TwoColumnLayout from '@/components/views/homepage/layouts/TwoColumnLayout.astro';
import Newsletter from '@/components/views/homepage/Newsletter.astro';
import AsymmetricLayout from '@/components/views/homepage/layouts/AsymmetricLayout.astro';

// --- Data Fetching ---
// We will fetch all necessary data for the homepage in parallel.
// Using `featured: true` where possible to get curated content.

const [
  heroArticlesData,
  brandsData,
  dealsData,
  featuredCategoriesData,
  featuredArticlesData,
  couponsData,
  tagsData,
  latestArticlesData,
  trendingArticlesData,
] = await Promise.all([
  Api.Article.getArticleList({ page_size: 5, sort_by: 'publish_date', sort_direction: 'desc' }),
  Api.Brand.getBrandList({ page_size: 24 }),
  Api.Deal.getDealList({ page_size: 12, featured: true }),
  Api.Category.getCategoryList({ page_size: 6, featured: true }),
  Api.Article.getArticleList({ page_size: 4, featured: true }),
  Api.Coupon.getCouponList({ page_size: 8, featured: true }),
  Api.Tag.getTagList({ page_size: 25 }),
  Api.Article.getArticleList({ page_size: 6, sort_by: 'publish_date', sort_direction: 'desc' }),
  Api.Article.getArticleList({ page_size: 10, sort_by: 'views', sort_direction: 'desc' }), // Fetch trending articles
]).catch(err => {
  console.error("Failed to fetch homepage data:", err);
  // Provide fallback empty data to prevent render errors
  return [{}, {}, {}, {}, {}, {}, {}, {}, {}];
});

// --- Data Processing ---
const heroArticles = heroArticlesData?.article_list || [];
const heroArticleForDisplay = heroArticles.find(article => article.featured_image) || heroArticles[0];
const brands = brandsData?.brand_list || [];
const deals = dealsData?.deal_list || [];
const featuredCategories = featuredCategoriesData?.category_list || [];
const featuredArticles = featuredArticlesData?.article_list || [];
const coupons = couponsData?.coupon_list || [];
const tags = tagsData?.tag_list || [];
const latestArticles = latestArticlesData?.article_list || [];
const trendingArticles = trendingArticlesData?.article_list || [];

// Fetch articles for the first two featured categories
const categoryArticleRequests = featuredCategories.slice(0, 2).map(category => 
  Api.Article.getArticleList({ page_size: 4, category: category.slug })
);
const categoryArticlesResults = await Promise.all(categoryArticleRequests).catch(err => {
    console.error("Failed to fetch category articles:", err);
    return [];
});

const categoryArticles = featuredCategories.slice(0, 2).map((category, index) => ({
  ...category,
  articles: categoryArticlesResults[index]?.article_list || [],
}));

---

<MainLayout 
  title="BrandReviews | Expert Reviews, Unbiased Ratings, and Smart Deals"
  description="The ultimate destination for in-depth product analysis, expert-led reviews, and exclusive deals to help you make smarter purchasing decisions."
>
  <!-- 
    The new homepage is built with a component-based architecture. 
    Each section is a separate Astro component, making the code clean, reusable, and easy to manage.
    This approach allows for complex and varied layouts while maintaining performance.
  -->

  <div class="flex flex-col space-y-24 md:space-y-32 overflow-hidden">

    <Hero articles={heroArticles} backgroundArticle={heroArticleForDisplay} class="animate-on-scroll" />

    <FeaturedBrands brands={brands} class="animate-on-scroll" />

    <FeaturedArticles articles={featuredArticles} class="animate-on-scroll" />

    <HotDeals deals={deals} class="animate-on-scroll" />

    <FeaturedCategories categories={featuredCategories} class="animate-on-scroll" />

    <TrendingArticles articles={trendingArticles} class="animate-on-scroll" />

    <PopularTags tags={tags} class="animate-on-scroll" />

    {categoryArticles.map(categoryData => (
      <CategoryArticles category={categoryData} class="animate-on-scroll" />
    ))}

    <ExclusiveCoupons coupons={coupons} class="animate-on-scroll" />

    <LatestArticles articles={latestArticles} class="animate-on-scroll" />

    <Newsletter class="animate-on-scroll" />

    <section class="container mx-auto px-4 py-16 animate-on-scroll">
      <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">More Popular Brands</h2>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {brands.slice(0, 8).map((brand, index) => (
          <a href={`/brand/${brand.slug}`} class="block bg-dark-surface rounded-2xl p-6 text-center group transition-transform duration-300 hover:-translate-y-2 hover:shadow-glow-md" style={`--glow-color: theme('colors.brand.blue');`}>
            <div class="h-20 flex items-center justify-center mb-4">
              <Image 
                src={brand.logo || '/assets/images/brand-placeholder.svg'}
                alt={`${brand.name} logo`}
                width={100}
                height={60}
                class="max-h-16 max-w-[100px] object-contain transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <h3 class="text-lg font-bold text-dark-on-surface">{brand.name}</h3>
          </a>
        ))}
      </div>
    </section>

  </div>

</MainLayout>