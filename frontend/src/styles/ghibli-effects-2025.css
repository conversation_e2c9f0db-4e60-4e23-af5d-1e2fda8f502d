/* ===== 2025 ULTIMATE STUDIO GHIBLI VISUAL EFFECTS LIBRARY ===== */
/* The most advanced and magical visual effects inspired by <PERSON> Ghibli's masterpieces */
/* Optimized for performance, accessibility, and cross-browser compatibility */

/* ===== CSS CUSTOM PROPERTIES FOR DYNAMIC THEMING ===== */
:root {
  /* Dynamic Animation Properties */
  --ghibli-float-duration: 12s;
  --ghibli-particle-speed: 20s;
  --ghibli-wind-speed: 15s;
  --ghibli-glow-intensity: 0.6;
  --ghibli-mouse-x: 50%;
  --ghibli-mouse-y: 50%;
}

/* Quantum Glow Enhancement */
.ghibli-glow-enhanced {
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.ghibli-glow-enhanced::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    rgba(0, 207, 255, 0.2), 
    rgba(0, 207, 255, 0.2), 
    rgba(0, 207, 255, 0.2), 
    rgba(0, 207, 255, 0.2));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: quantumGlow 4s ease-in-out infinite;
}

.ghibli-glow-enhanced:hover::before {
  opacity: 1;
}

@keyframes quantumGlow {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: blur(2px);
  }
  50% { 
    background-position: 100% 50%; 
    filter: blur(4px);
  }
}



/* Mouse Light Following Effect */
.ghibli-mouse-light {
  position: relative;
}

.ghibli-mouse-light::after {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  left: var(--mouse-x, 50%);
  top: var(--mouse-y, 50%);
  transform: translate(-50%, -50%);
}

.ghibli-mouse-light:hover::after {
  opacity: 1;
  animation: mouseLightPulse 2s ease-in-out infinite;
}

@keyframes mouseLightPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
}














