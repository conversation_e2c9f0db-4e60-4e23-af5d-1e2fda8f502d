/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        dark: {
          'bg': '#121212',
          'surface': '#1E1E1E',
          'primary': '#BB86FC',
          'secondary': '#03DAC6',
          'on-primary': '#000000',
          'on-surface': '#FFFFFF',
          'border': '#2A2A2A',
        },
        light: {
          'bg': '#F5F5F5',
          'surface': '#FFFFFF',
          'primary': '#6200EE',
          'secondary': '#03DAC6',
          'on-primary': '#FFFFFF',
          'on-surface': '#000000',
          'border': '#E0E0E0',
        },
        brand: {
          'blue': '#00CFFF', // Slightly brighter blue
          'purple': '#A020F0', // More vibrant purple
        }
      },
      fontFamily: {
        sans: ['Montserrat', 'Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', '"Noto Sans"', 'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"', '"Noto Color Emoji"'],
      },
      boxShadow: {
        'glow-sm': '0 0 8px rgba(var(--glow-color), 0.6)',
        'glow-md': '0 0 20px rgba(var(--glow-color), 0.7)',
        'glow-lg': '0 0 40px rgba(var(--glow-color), 0.8)',
      },
      animation: {
        'aurora': 'aurora 60s linear infinite',
      },
      keyframes: {
        aurora: {
          'from': {
            'background-position': '0% 50%',
          },
          'to': {
            'background-position': '200% 50%',
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography')(({ theme }) => ({
      DEFAULT: {
        css: {
          color: theme('colors.dark.on-surface'),
          // General text and links
          a: {
            color: theme('colors.brand.blue'),
            textDecoration: 'none',
            '&:hover': {
              color: theme('colors.brand.blue'),
              textDecoration: 'underline',
            },
          },
          p: {
            lineHeight: '1.75', // Improved readability
            marginBottom: '1.25em',
          },
          // Headings
          h1: {
            color: theme('colors.dark.on-surface'),
            marginTop: '2em',
            marginBottom: '1em',
            lineHeight: '1.2',
          },
          h2: {
            color: theme('colors.dark.on-surface'),
            marginTop: '1.75em',
            marginBottom: '0.75em',
            lineHeight: '1.3',
          },
          h3: {
            color: theme('colors.dark.on-surface'),
            marginTop: '1.5em',
            marginBottom: '0.5em',
            lineHeight: '1.4',
          },
          h4: {
            color: theme('colors.dark.on-surface'),
            marginTop: '1.25em',
            marginBottom: '0.25em',
            lineHeight: '1.5',
          },
          strong: {
            color: theme('colors.dark.on-surface'),
          },
          // Code blocks
          code: {
            color: theme('colors.dark.on-surface'),
            backgroundColor: theme('colors.dark.surface'),
            padding: '0.2em 0.4em',
            borderRadius: '0.25em',
            fontSize: '0.875em', // Slightly smaller for inline code
          },
          'code::before': { content: '""' }, // Remove default backtick
          'code::after': { content: '""' }, // Remove default backtick
          pre: {
            backgroundColor: theme('colors.dark.surface'),
            color: theme('colors.dark.on-surface'),
            padding: '1em',
            borderRadius: '0.5em',
            overflowX: 'auto', // Enable horizontal scrolling for long lines
            fontSize: '0.9em',
            lineHeight: '1.5',
            code: {
              backgroundColor: 'transparent', // Don't double-apply background
              color: 'inherit',
              padding: '0',
              borderRadius: '0',
            },
          },
          // Blockquotes
          blockquote: {
            color: theme('colors.dark.on-surface'),
            borderLeftColor: theme('colors.brand.blue'),
            borderLeftWidth: '4px',
            paddingLeft: '1em',
            fontStyle: 'italic',
            opacity: '0.8',
          },
          // Lists
          li: {
            color: theme('colors.dark.on-surface'),
            marginBottom: '0.5em',
          },
          ol: {
            li: {
              '&::marker': {
                color: theme('colors.dark.on-surface'),
              },
            },
          },
          ul: {
            li: {
              '&::marker': {
                color: theme('colors.dark.on-surface'),
              },
            },
          },
          // Images
          img: {
            borderRadius: '0.5em',
            display: 'block',
            margin: '1em auto',
            maxWidth: '100%',
            height: 'auto',
          },
          // Tables
          table: {
            width: '100%',
            borderCollapse: 'collapse',
            marginTop: '1.5em',
            marginBottom: '1.5em',
            fontSize: '0.9em',
            lineHeight: '1.5',
            'th, td': {
              border: `1px solid ${theme('colors.dark.border')}`,
              padding: '0.75em 1em',
              textAlign: 'left',
            },
            th: {
              backgroundColor: theme('colors.dark.surface'),
              fontWeight: 'bold',
              color: theme('colors.dark.on-surface'),
            },
            'tbody tr:nth-child(odd)': {
              backgroundColor: theme('colors.dark.surface'),
            },
          },
        },
      },
    })),
  ],
};